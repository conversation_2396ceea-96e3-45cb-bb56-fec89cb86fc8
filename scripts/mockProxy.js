function parseJson(req) {
    return new Promise((resolve) => {
        let body = '';
        let jsonStr = '';
        req.on('data', (chunk) => {
            body += chunk;
        });
        req.on('end', () => {
            try {
                jsonStr = JSON.parse(body);
            } catch (err) {
                jsonStr = '';
            }
            resolve(jsonStr);
        });
    });
}

/** @type {import('vite').Plugin} */
export default function (options){
    return {
        name: 'mock-proxy-plugin',
        configureServer({ middlewares }) {
            const mockMaps = options.mockMaps;
            Object.keys(mockMaps).forEach((path) => {
                middlewares.use(path, async (req, res) => {
                    // 设置响应头
                    res.setHeader('Content-Type', 'application/json');
                    const content = mockMaps[path];
    
                    if (req.method === 'POST') {
                        req.body = await parseJson(req);
                    }
    
                    if (typeof content.default === 'function') {
                        res.end(JSON.stringify(await content.default(req, res)));
                    }
    
                    if (typeof content.default === 'object') {
                        res.end(JSON.stringify(await content.default));
                    }
    
                    if (typeof content === 'function') {
                        res.end(JSON.stringify(await content(req, res)));
                    }
                    if (typeof content === 'object' && !content.default) {
                        res.end(JSON.stringify(content));
                    }
                });
            });
        },
    };
    
}