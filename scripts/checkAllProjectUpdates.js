import { walk } from './utils'
import fs from 'fs'
import path from 'path'
//跑一遍每个文件得到 [fMap]:[f1,f2,...]
//即 f1,f2影响了f
//又根据时间得到改变的文件列表 P:[f1,f2,...]
//根据P得到K:[f1,f2,...]即为被影响的文件列表
//取出P中属于pages的 取出K中属于pages的

var fMap = {}
var P = []
var K = []
var lastRunTime = new Date(fs.readFileSync('./scripts/lastRanTime').toString())
if (!lastRunTime) {
  const date = new Date()
  date.setDate(date.getDate() - 1)
  lastRunTime = date.getTime()
}
console.log('run at', lastRunTime.toISOString())

const buildFMap = f => {
  const reg = /import.*from ["'](.*?)["']/g
  const fr = fs.readFileSync(f)
  var content = fr.toString()
  const rs = content.matchAll(reg)
  for (var matched of rs) {
    const relativePath = matched[1]
    var fname = ''
    if (!relativePath.includes('kit/')) {
      const fmtPath = path.normalize(path.dirname(f) + '/' + relativePath)
      if (!fMap[f]) {
        fMap[f] = []
      }
      fname = fmtPath
    } else {
      if (!fMap[f]) {
        fMap[f] = []
      }
      fname = relativePath.replace('kit/', 'src/')
    }

    //补全扩展名
    if (!fname.includes('.vue') && !fname.includes('.js')) {
      fname += '.js'
    }

    fMap[f].push(fname)
  }
}

const buildP = f => {
  const mtime = fs.statSync(f).mtime.getTime()
  if (mtime > lastRunTime.getTime()) {
    P.push(f)
  }
}

walk('src', f => {
  const ext = path.extname(f)
  if (!(ext === '.js' || ext === '.vue')) {
    return
  }
  buildFMap(f)
  buildP(f)
})

// fs.writeFileSync('./scripts/fmap.json', JSON.stringify(fMap))
// console.log('P', P)

var m = {}
for (var c of P) {
  if (c.includes('pages/')) {
    m[c] = 1
    continue
  }
  for (var sf in fMap) {
    if (fMap[sf].includes(c)) {
      if (sf.includes('pages/')) {
        m[sf] = 1
      }
    }
  }
}

const allFiles = Object.keys(m)
if (!allFiles.length) {
  console.log('✅All right, cool!')
}
var groupFiles = {}
var reg = /\/pages\/([A-z0-9]+)\//g
var reg2 = /\/pages\/([A-z0-9]+\/[A-z0-9]+)/g
for (var f of allFiles) {
  var rs = null

  if (f.includes('marketing')) {
    rs = f.matchAll(reg2)
  } else {
    rs = f.matchAll(reg)
  }

  const key = rs.next().value[1]
  if (!groupFiles[key]) {
    groupFiles[key] = []
  }

  groupFiles[key].push(f)
}

for (var group in groupFiles) {
  console.log('project', group)
  groupFiles[group].sort()
  console.log(groupFiles[group].join('\n'))

  console.log('\n')
}

fs.writeFileSync('./scripts/lastRanTime', new Date().toISOString())
