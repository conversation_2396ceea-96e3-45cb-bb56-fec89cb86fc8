const parsePassedArguments = require('./helpers/parsePassedArguments')
const walkFile = require('./helpers/walkFile')
const readFileToLines = require('./helpers/readFileToLines')
const fs = require('fs')
const path = require('path')

const arguments = parsePassedArguments(process.argv)

const project = arguments.project
if (!project) {
  throw new Error('--project is required')
}

const capitalized = word => word.charAt(0).toUpperCase() + word.slice(1)

// export default class extends Vue
const addDefaultClassName = (filePath, fileLines) => {
  var isNeedUpdate = false
  for (var i = 0; i < fileLines.length; i++) {
    if (fileLines[i].includes(`export default class extends`)) {
      const bn = path.basename(filePath)
      const ext = path.extname(filePath)
      const newName = capitalized(bn.replace(ext, ''))

      fileLines[i] = fileLines[i].replace(
        'export default class',
        `export default class ${newName}`
      )
      isNeedUpdate = true
    }
  }

  return isNeedUpdate
}
walkFile(project, filePath => {
  var fileLines = readFileToLines(filePath)
  const isNeedUpdate = addDefaultClassName(filePath, fileLines)
  if (isNeedUpdate) {
    fs.writeFileSync(filePath, fileLines.join('\n'))
  }
})

console.log('Done')
