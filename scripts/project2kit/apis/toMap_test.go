package apis

import "testing"

func Test_toMap(t *testing.T) {
	exports := []string{
		`export const getBudgetSchemeList = (parameters) => {
			return post("/api/expense/budget/scheme/list", parameters)
		}`,
		`export const getBudgetSchemeDetail = (parameters) => {
			return get("/api/expense/budget/scheme/detail", parameters)
		}`,
	}

	m, _ := toMap("fake file", exports)

	if len(m) != 2 {
		t.Error("toMap error")
	}
	r, ok := m["getBudgetSchemeList"]
	if !ok {
		t.<PERSON>rror("toMap error")
	}
	if r[0] != "/api/expense/budget/scheme/list" {
		t.<PERSON>rror("toMap error")
	}
	if r[1] != "expenseBudgetSchemeList" {
		t.Error("toMap error")
	}
}
