package apis

import (
	"regexp"
	"strings"
)

func oldAPIName(export string) string {
	reg := regexp.MustCompile(`export const ([\w0-9]+) =`)
	matchers := reg.FindStringSubmatch(export)
	if len(matchers) < 2 {
		panic("can not find old api name\n" + export)
	}

	return matchers[1]
}

func methodAndResourceName(export string) (string, string) {
	reg := regexp.MustCompile("(post|get)\\([\\n\\s]*[\"`]{1}([A-z0-9/]+)[\"`]+")
	matchers := reg.FindStringSubmatch(export)
	if len(matchers) < 2 {
		// println(export)
		return "", ""
	}

	return matchers[1], matchers[2]
}
func newAPIName(resourceName string) string {
	if resourceName == "" {
		return ""
	}
	ss := strings.Split(resourceName, "/")
	r := make([]string, 0)
	for index, c := range ss {
		if index < 2 {
			continue
		}

		if index > 2 {
			c = strings.Title(c)
		}

		r = append(r, c)
	}

	return strings.Join(r, "")
}

func toMap(path string, exports []string) (matched, noMatched map[string][]string) {
	matched = make(map[string][]string)
	noMatched = make(map[string][]string)
	for _, export := range exports {
		oldAPIName := oldAPIName(export)
		method, resourceName := methodAndResourceName(export)
		var apiName string
		if resourceName != "" {
			apiName = newAPIName(resourceName)
		}

		if apiName == "" {
			noMatched[path] = append(noMatched[path], export)
			continue
		}

		matched[oldAPIName] = []string{resourceName, apiName, method}
	}

	return
}
