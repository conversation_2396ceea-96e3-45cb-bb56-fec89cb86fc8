package helpers

import "testing"

func Test_ParseExports(t *testing.T) {
	lines := ReadFileToLines("testdata/programme.js")

	exports := ParseExports(lines)
	if len(exports) != 16 {
		t.<PERSON><PERSON><PERSON>("len(exports) = %d, want 16", len(exports))
	}

}

func Test_findPrevMatchStringIndex(t *testing.T) {
	steps := FindPrevMatchString([]string{
		"import {",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  getDictListByTypesAndCodes,",
	}, 4, "import {")

	if steps != 4 {
		t.Error("steps should be 4")
	}

	steps = FindPrevMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  </div>,",
	}, 3, "<")

	if steps != 3 {
		t.Error("steps should be 3")
	}

	//-1 case
	steps = FindPrevMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
		"  getDictListByType,",
	}, 2, "import")

	if steps != -1 {
		t.Error("steps should be -1")
	}

}

func Test_findNexMatchStringIndex(t *testing.T) {
	steps := FindNexMatchString([]string{
		"import {",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  getDictListByTypesAndCodes,",
	}, 1, "getDictListByType")

	if steps != 1 {
		t.Errorf("steps should be 1, but got %d", steps)
	}

	steps = FindNexMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
		"  getDictListByType,",
		"  getDictListByTypes,",
		"  </div>,",
	}, 1, ">")

	if steps != 3 {
		t.Error("steps should be 3")
	}

	//-1 case
	steps = FindNexMatchString([]string{
		"<div class=\"form-item\">",
		"  getDictList,",
	}, 1, "import")

	if steps != -1 {
		t.Error("steps should be -1")
	}
}
