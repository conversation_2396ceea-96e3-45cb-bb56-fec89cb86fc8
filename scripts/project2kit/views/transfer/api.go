package transfer

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/alading/scm/front/kit/scripts/project2kit/helpers"
)

func getAPIsFromLines(lines []string) (apis []string) {
	s := strings.Join(lines, "\n")
	reg := regexp.MustCompile("([A-z0-9]+)")
	ss := reg.FindAllString(s, -1)
	for _, api := range ss {
		if api == "import" {
			continue
		}
		if api == "from" {
			break
		}
		apis = append(apis, api)
	}
	return
}
func getAPIFromLines(lines []string) (api string) {
	s := strings.Join(lines, "\n")
	reg := regexp.MustCompile("([A-z0-9]+)\\(")
	ss := reg.FindStringSubmatch(s)
	if len(ss) > 0 {
		api = ss[1]
	}

	return
}
func transAPIsImport(lines []string, currentIndex int, fpath, service string) (
	newLines []string, start, end int) {
	line := lines[currentIndex]

	importSteps := helpers.FindPrevMatchString(lines, currentIndex, "import ")

	end = currentIndex
	start = end - importSteps
	content := lines[start : end+1]
	apis := getAPIsFromLines(content)

	imports := make([]string, 0)
	isPlatform := false
	if strings.Contains(line, "/api/platform") {
		isPlatform = true
		for _, api := range apis {
			mApi := platformAPIsMap[api]
			if mApi == "" {
				mApi = api
			}

			imports = append(imports, mApi)
			if platformAPIsMap[api] == "" {
				println(fmt.Sprintf("platform[%s] is empty", api))
			}

		}
	} else {
		for _, api := range apis {
			mApi := apisMap[api]
			if mApi == "" {
				mApi = api
			}
			imports = append(imports, mApi)
			if apisMap[api] == "" {
				println(fmt.Sprintf("[%s] is empty", api))
			}
		}
	}

	if !isPlatform {
		newLines = append(newLines, "import { "+strings.Join(imports, ", ")+" } from '@/api/"+service+"'")
	} else {
		newLines = append(newLines, "import { "+strings.Join(imports, ", ")+" } from '@/api/platform'")
	}

	// println("find")
	// println(strings.Join(content, "\n"))
	// println("replace")
	// println(strings.Join(newLines, "\n"))
	// println()

	return
}

// 只略过不处理的情况，其他情况统一加入err，由编辑来干预变量重定义问题
func transAPIsUse(lines []string, currentIndex int) (
	newLines []string, start, end int) {
	//await form.getFormData 不处理
	//this.profile = await getProfileApi() 需要进行特殊赋值
	//const { data } = await getUserList({ //需要进行特殊赋值
	//     name: this.form.name,
	//     age: this.form.age,
	//     phone: this.form.phone,
	//     hobby: this.form.hobby,
	//     page: this.currentPage,
	//     pageSize: this.pageSize
	//   })
	//await this.oTable.appendRequestParams(params) 不处理
	//const _data = await getCustomerContractDetailApi({ //需要进行错误处理与赋值
	// id: this.id,
	// });
	//await this.eachJsonFormRef 不处理
	//await Promise.all 不处理 人工干预

	// await this.loadDetail() 两个在一起 需要err2
	//     await this.loadTableList()

	// await addAccountantApi(params) 仅处理错误
	// if (this.clickReminderItem.id) { 前面一到两行有大括号 可以采用简单的err
	// 	await updateCalendarManagementApi(params)
	//   } else {
	// 	await addCalendarManagementApi(params)
	//   }
	line := lines[currentIndex]
	if strings.Contains(line, "downloadBankPayFileApi") {
		println(1)
	}
	noNeedHandle := []string{
		"Promise.all", "eachJsonFormRef", "oTable", "getFormData",
		"this.", "err", "loadTableList", "receiptUtils.",
	}
	//不需要处理的情况
	for _, s := range noNeedHandle {
		if strings.Contains(line, s) {
			return nil, -1, -1
		}
	}
	usedSteps := helpers.FindNexMatchString(lines, currentIndex, ")")

	if strings.Contains(line, "({") {
		//处理跨行情况
		// const data = await getBillItemListApi({
		//     billIds: [...new Set(billIds)]
		//   })
		// checkCounts = await importPayCheckResult({
		// 	checkDate: Number(formData.get('checkDate')),
		// 	fileName: formData.get('fileName'),
		// 	fileType: formData.get('fileType')
		//   })
		newSteps := helpers.FindNexMatchString(lines, currentIndex, "})")
		// 特殊情况
		// await downloadBankPayFileApi({ id:row.id }, true)
		if newSteps != -1 {
			usedSteps = newSteps
		}
	}

	//特殊场景 这里是从hro拷贝过来的 后面需要修正
	paramsHadBrackets := helpers.FindNexMatchString(lines, currentIndex, ".filter")
	if paramsHadBrackets-usedSteps == 1 {
		usedSteps = usedSteps + 2
	}

	end = currentIndex + usedSteps
	start = currentIndex
	content := lines[start : end+1]
	// println("find")
	// println(strings.Join(content, "\n"))

	oldAPI := getAPIFromLines(content)
	// println("api:", oldAPI)
	if oldAPI == "" {
		panic("api not found")
	}
	newAPI := apisMap[oldAPI]
	if newAPI == "" {
		newAPI = platformAPIsMap[oldAPI]
	}
	//没有对应关系 不处理
	if newAPI == "" {
		return nil, -1, -1
	}

	//处理赋值
	///没有const赋值情况
	assign := ""
	if !strings.Contains(line, "const") {
		//在前面已经定义过值
		if strings.Contains(line, "=") {
			tmp := strings.Split(line, "=")
			reg := regexp.MustCompile("([A-z0-9]+)")
			ss := reg.FindStringSubmatch(tmp[0])
			content[0] = "const [err,r] = " + tmp[1]
			assign = ss[1] + " = r"
		} else {
			content[0] = "const [err] = " + line
		}
	} else {
		tmp := strings.Split(line, "=")
		if strings.Contains(tmp[0], "{") && strings.Contains(tmp[0], "}") {
			//todo 赋值操作
			reg := regexp.MustCompile("const (\\{\\s+[A-z0-9]+\\s+\\})")
			ss := reg.FindStringSubmatch(line)
			name := strings.TrimSpace(ss[1])
			content[0] = reg.ReplaceAllString(content[0], fmt.Sprintf("const [err, %s]", name))
		} else {
			reg := regexp.MustCompile("const ([A-z0-9]+)")
			ss := reg.FindStringSubmatch(line)
			name := strings.TrimSpace(ss[1])
			content[0] = reg.ReplaceAllString(content[0], fmt.Sprintf("const [err, %s]", name))
		}
	}

	api := platformAPIsMap[oldAPI]
	if api == "" {
		api = apisMap[oldAPI]
	}
	if api == "" {
		api = oldAPI
	}
	content[0] = strings.ReplaceAll(content[0], oldAPI, api)

	//处理参数
	if !strings.Contains(content[0], "()") {
		content[0] = strings.ReplaceAll(content[0], "(", "({body:")
		last := len(content) - 1
		content[last] = strings.ReplaceAll(content[last], ")", "})")
	}

	newLines = append(newLines, content...)

	newLines = append(newLines, []string{
		"if (err) {",
		"\thandleError(err)",
		"\treturn",
		"}",
	}...)
	if assign != "" {
		newLines = append(newLines, assign)
	}

	// println("replace")
	// println(strings.Join(newLines, "\n"))
	// println()

	return
}

var errIndex = 1

func transAPIs(fpath string, lines []string, service string) (newFilePath string, newLines []string) {
	results := linesIterator(lines, func(lines []string, currentIndex int) (
		newLines []string, start, end int) {
		if strings.Contains(lines[currentIndex], "@/api/") {
			return transAPIsImport(lines, currentIndex, fpath, service)
		}

		if strings.Contains(lines[currentIndex], "await ") {
			// println(fpath)
			return transAPIsUse(lines, currentIndex)
		}

		return nil, -1, -1
	})

	return fpath, results
}
