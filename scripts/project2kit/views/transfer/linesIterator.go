package transfer

type codeBlockReplacer func(lines []string, currentIndex int) (
	newLines []string, start, end int)

func linesIterator(lines []string, replacer codeBlockReplacer) (result []string) {
	var i int
	for i = 0; i < len(lines); {
		//如果命中了某行，则在lines拆出一段，处理后返回
		newLines, start, end := replacer(lines, i)
		if start != -1 && start >= i {
			result = append(result, lines[i:start]...)
		}

		//回退
		if end != -1 && start != -1 && start < i {
			result = result[:start]
		}

		if end != -1 {
			i = end + 1
		}

		if len(newLines) > 0 {
			result = append(result, newLines...)
		}

		if len(newLines) == 0 {
			result = append(result, lines[i])
			i++
		}
	}

	return
}
