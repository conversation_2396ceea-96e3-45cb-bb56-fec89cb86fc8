package contract

import (
	"bufio"
	"io/ioutil"
	"os"
	"regexp"
	"strings"

	"github.com/alading/scm/front/kit/scripts/contract2kit/formatters/contract/components"
	"github.com/alading/scm/front/kit/scripts/contract2kit/formatters/contract/formatters"
	"github.com/alading/scm/front/kit/scripts/contract2kit/formatters/contract/pages"
)

func readFileToLines(path string) []string {
	f, err := os.Open(path)
	if err != nil {
		panic(err)
	}
	defer f.Close()

	var lines []string
	scanner := bufio.NewScanner(f)
	scanner.Split(bufio.ScanLines)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	return lines
}
func writeLineToFile(path string, lines []string) {
	content := strings.Join(lines, "\n")

	err := ioutil.WriteFile(path, []byte(content), 0644)
	if err != nil {
		panic(err)
	}

}

func isImportLine(line string) bool {
	reg := regexp.MustCompile(`from ['"].*?['"]`)

	return reg.Match([]byte(line))
}

// makePlatformClient 由人工维护
func Format(path, scope, f string) {
	lines := readFileToLines(f)
	for index, line := range lines {
		if !isImportLine(line) || line == "" {
			continue
		}
		var formattedImportLine string

		switch scope {
		case "pages":
			formattedImportLine = pages.Format(path, f, line)
		case "components":
			formattedImportLine = components.Format(path, f, line)
		case "formatters":
			formattedImportLine = formatters.Format(path, f, line)
		}

		lines[index] = formattedImportLine
	}

	p := strings.Join(lines, "\n")
	err := ioutil.WriteFile(f, []byte(p), 0644)
	if err != nil {
		panic(err)
	}
}
