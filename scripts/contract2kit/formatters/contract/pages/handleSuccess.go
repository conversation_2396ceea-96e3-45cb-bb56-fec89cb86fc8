package pages

import (
	"fmt"
	"strings"
)

func formatHandleSuccess(f, line string) (formattedLine string) {
	if strings.Contains(line, "handleSuccess") {
		if strings.Contains(line, "../../helpers") {
			return fmt.Sprintf("import handleError from '../../../helpers/handleError'\nimport handleSuccess from '../../../helpers/handleSuccess'")
		}
		if strings.Contains(line, "../helpers") {
			return fmt.Sprintf("import handleError from '../../helpers/handleError'\nimport handleSuccess from '../../helpers/handleSuccess'")
		}
	}

	return line
}
