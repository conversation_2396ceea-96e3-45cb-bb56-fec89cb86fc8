package transfer

import (
	"bufio"
	"os"
	"strings"
)

var platformAPIsMap map[string]string
var hroAPIsMap map[string]string

// getPreviewFileUrl getCaptchaURL
func init() {
	platformAPIsMap = map[string]string{
		"getPlatformLegalApi":               "merchantPlatformListLegal",
		"loginApi":                          "merchantPlatformLogin",
		"createCaptchaApi":                  "merchantPlatformCreateCaptcha",
		"createOtpApi":                      "merchantPlatformCreateOtp",
		"uploadFileApi":                     "merchantPlatformUploadFile",
		"modifyPasswordApi":                 "merchantPlatformModifyPassword",
		"listFileApi":                       "merchantPlatformListFile",
		"addCalendarManagementApi":          "calendarManagementAdd",
		"getOneDayListApi":                  "calendarManagementGetOneDayList",
		"getCalendarManagementListApi":      "calendarManagementGetList",
		"updateStatusCalendarManagementApi": "calendarManagementUpdateStatus",
		"updateCalendarManagementApi":       "calendarManagementUpdate",
		"getProfileApi":                     "merchantPlatformProfile",
	}

	hroAPIsMap = map[string]string{
		"getCustomerCorporationListApi":          "hroCustomerCorporationList",
		"addCustomerContractApi":                 "hroCustomerContractAccountItems",
		"getCustomerContractDetailApi":           "hroCustomerContractDetail",
		"contractRenewApi":                       "hroSupplierContractRenew",
		"getCustomerContractListApi":             "hroCustomerContractList",
		"customerContractStopApi":                "hroCustomerContractStop",
		"addCustomerApi":                         "hroCustomerAdd",
		"getCustomerBasicDetailApi":              "hroCustomerBasicDetail",
		"getCustomerListApi":                     "hroCustomerList",
		"billListApi":                            "hroPaymentOffsetList",
		"detailApi":                              "hroPaymentDetail",
		"getBillListApi":                         "hroBillList",
		"offsetApi":                              "hroPaymentOffset",
		"getPaymentListApi":                      "hroPaymentList",
		"getBillItemListApi":                     "hroBillItemList",
		"invoiceDetailApi":                       "hroInvoiceDetail",
		"invoiceMakeApi":                         "hroInvoiceMake",
		"invoiceBackApi":                         "hroInvoiceBack",
		"itemExportApi":                          "hroBillItemExport",
		"billOffsetApi":                          "hroBillOffset",
		"getBillDetailApi":                       "hroBillDetail",
		"getBillItemDetailApi":                   "hroBillBillItemDetail",
		"getEmployeeOtherFeeListApi":             "hroBillEmployeeOtherFeeList",
		"addBillListApi":                         "hroBillAdd",
		"addInvoiceApi":                          "hroInvoiceAdd",
		"invoiceInvalidApi":                      "hroInvoiceInvalid",
		"invoiceReceivedApi":                     "hroInvoiceReceived",
		"addSupplierContractApi":                 "hroSupplierContractRenew",
		"getSupplierContractDetailApi":           "hroSupplierContractDetail",
		"getSupplierContractListApi":             "hroSupplierContractList",
		"addSupplierApi":                         "hroSupplierAdd",
		"getSupplierBasicDetailApi":              "hroSupplierBasicDetail",
		"getSupplierCorporationListApi":          "hroSupplierCorporationList",
		"getSupplierListApi":                     "hroSupplierList",
		"getServiceContentApi":                   "",
		"updateCustomerContractApi":              "hroCustomerContractUpdate",
		"updateCustomerContractManagerApi":       "hroCustomerContractManagerUpdate",
		"updateCustomerApi":                      "hroCustomerUpdate",
		"updateCustomerStatusApi":                "hroCustomerStatusUpdate",
		"getAddressListApi":                      "",
		"invoiceListApi2":                        "hroInvoicePage2",
		"getAccountsetItemListApi":               "hroAccountsetItemBillList",
		"invoiceUpdateApplyApi":                  "hroInvoiceUpdate",
		"getAccountantDetailApi":                 "hroAccountsetDetail",
		"invoiceListApi":                         "hroInvoicePage",
		"addAccountantApi":                       "hroAccountsetAdd",
		"updateAccountantApi":                    "hroAccountsetUpdate",
		"getAccountCustomerContractListApi2":     "", //被封装了 手动改
		"getAccountCustomerContractItemListApi2": "",
		"addInvoiceContentApi":                   "hroContentAdd",
		"getInvoiceContentListApi":               "",
		"disableAccountantApi":                   "hroAccountsetDisable",
		"enableAccountantApi":                    "hroAccountsetEnable",
		"getAccountantListApi":                   "hroAccountsetPage",
		"getByIdSupplierCorporationListApi":      "",
		"updateSupplierContractApi":              "hroSupplierContractUpdate",
		"stopSupplierContractApi":                "hroSupplierContractStop",
		"updateContractManagerApi":               "hroSupplierContractManagerUpdate",
		"updateSupplierApi":                      "hroSupplierUpdate",
		"getUserList":                            "", //看起来是一个假的api
	}
}

func findPrevMatchString(lines []string, start int, s string) (prevSteps int) {
	for i := start; i >= 0; i-- {
		if strings.Contains(lines[i], s) {
			return start - i
		}
	}

	return -1
}

func findNexMatchString(lines []string, start int, s string) (nextSteps int) {
	for i := start; i < len(lines); i++ {
		if strings.Contains(lines[i], s) {
			return i - start
		}
	}

	return -1
}

func readFileToLines(path string) []string {
	f, err := os.Open(path)
	if err != nil {
		panic(err)
	}
	defer f.Close()

	var lines []string
	scanner := bufio.NewScanner(f)
	scanner.Split(bufio.ScanLines)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	return lines
}
