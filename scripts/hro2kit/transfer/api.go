package transfer

import (
	"fmt"
	"regexp"
	"strings"
)

func getAPIsFromLines(lines []string) (apis []string) {
	s := strings.Join(lines, "\n")
	reg := regexp.MustCompile("([A-z0-9]+)")
	ss := reg.FindAllString(s, -1)
	for _, api := range ss {
		if api == "import" {
			continue
		}
		if api == "from" {
			break
		}
		apis = append(apis, api)
	}
	return
}
func getAPIFromLines(lines []string) (api string) {
	s := strings.Join(lines, "\n")
	reg := regexp.MustCompile("([A-z0-9]+)\\(")
	ss := reg.FindStringSubmatch(s)
	if len(ss) > 0 {
		api = ss[1]
	}

	return
}
func transAPIsImport(lines []string, currentIndex int) (
	newLines []string, start, end int) {
	line := lines[currentIndex]

	importSteps := findPrevMatchString(lines, currentIndex, "import ")

	end = currentIndex
	start = end - importSteps
	content := lines[start : end+1]
	apis := getAPIsFromLines(content)

	imports := make([]string, 0)
	isHRO := false
	if strings.Contains(line, "/api/platform") {
		for _, api := range apis {
			mApi := platformAPIsMap[api]
			if mApi == "" {
				mApi = api
			}

			imports = append(imports, mApi)
			if platformAPIsMap[api] == "" {
				println(fmt.Sprintf("platform[%s] is empty", api))
			}

		}
	} else {
		isHRO = true
		for _, api := range apis {
			mApi := hroAPIsMap[api]
			if mApi == "" {
				mApi = api
			}
			imports = append(imports, mApi)
			if hroAPIsMap[api] == "" {
				println(fmt.Sprintf("hro[%s] is empty", api))
			}
		}
	}

	if isHRO {
		newLines = append(newLines, "import { "+strings.Join(imports, ", ")+" } from '@/api/hro'")
	} else {
		newLines = append(newLines, "import { "+strings.Join(imports, ", ")+" } from '@/api/platform'")
	}

	// println("find")
	// println(strings.Join(content, "\n"))
	// println("replace")
	// println(strings.Join(newLines, "\n"))
	// println()

	return
}

// 只略过不处理的情况，其他情况统一加入err，由编辑来干预变量重定义问题
func transAPIsUse(lines []string, currentIndex int) (
	newLines []string, start, end int) {
	//await form.getFormData 不处理
	//this.profile = await getProfileApi() 需要进行特殊赋值
	//const { data } = await getUserList({ //需要进行特殊赋值
	//     name: this.form.name,
	//     age: this.form.age,
	//     phone: this.form.phone,
	//     hobby: this.form.hobby,
	//     page: this.currentPage,
	//     pageSize: this.pageSize
	//   })
	//await this.oTable.appendRequestParams(params) 不处理
	//const _data = await getCustomerContractDetailApi({ //需要进行错误处理与赋值
	// id: this.id,
	// });
	//await this.eachJsonFormRef 不处理
	//await Promise.all 不处理 人工干预

	// await this.loadDetail() 两个在一起 需要err2
	//     await this.loadTableList()

	// await addAccountantApi(params) 仅处理错误
	// if (this.clickReminderItem.id) { 前面一到两行有大括号 可以采用简单的err
	// 	await updateCalendarManagementApi(params)
	//   } else {
	// 	await addCalendarManagementApi(params)
	//   }
	line := lines[currentIndex]
	noNeedHandle := []string{
		"Promise.all", "eachJsonFormRef", "oTable", "getFormData", "this.", "err", "loadTableList",
	}
	//不需要处理的情况
	for _, s := range noNeedHandle {
		if strings.Contains(line, s) {
			return nil, -1, -1
		}
	}

	usedSteps := findNexMatchString(lines, currentIndex, ")")
	// const data = await getBillItemListApi({
	//     billIds: [...new Set(billIds)]
	//   })
	paramsHadBrackets := findNexMatchString(lines, currentIndex, "})")
	if paramsHadBrackets-usedSteps == 1 {
		usedSteps = usedSteps + 1
	}
	paramsHadBrackets = findNexMatchString(lines, currentIndex, ".filter")
	if paramsHadBrackets-usedSteps == 1 {
		usedSteps = usedSteps + 2
	}

	end = currentIndex + usedSteps
	start = currentIndex
	content := lines[start : end+1]
	// println("find")
	// println(strings.Join(content, "\n"))

	oldAPI := getAPIFromLines(content)
	// println("api:", oldAPI)
	if oldAPI == "" {
		panic("api not found")
	}

	//无赋值情况
	if !strings.Contains(line, "const") {
		//前一行有大括号 则可以采用简单的err
		content[0] = "const [err] = " + content[0]
	} else {
		if strings.Contains(line, "{") && strings.Contains(line, "}") {
			//todo 赋值操作
			reg := regexp.MustCompile("const (\\{\\s+[A-z0-9]+\\s+\\})")
			ss := reg.FindStringSubmatch(line)
			name := strings.TrimSpace(ss[1])
			content[0] = reg.ReplaceAllString(content[0], fmt.Sprintf("const [err, %s]", name))
		} else {
			reg := regexp.MustCompile("const ([A-z0-9]+)")
			ss := reg.FindStringSubmatch(line)
			name := strings.TrimSpace(ss[1])
			content[0] = reg.ReplaceAllString(content[0], fmt.Sprintf("const [err, %s]", name))
		}
	}

	api := platformAPIsMap[oldAPI]
	if api == "" {
		api = hroAPIsMap[oldAPI]
	}
	if api == "" {
		api = oldAPI
	}
	content[0] = strings.ReplaceAll(content[0], oldAPI, api)
	if !strings.Contains(content[0], "()") {
		content[0] = strings.ReplaceAll(content[0], "(", "({body:")
		last := len(content) - 1
		content[last] = strings.ReplaceAll(content[last], ")", "})")
	}

	newLines = append(newLines, content...)
	newLines = append(newLines, []string{
		"if (err) {",
		"\thandleError(err)",
		"\treturn",
		"}",
	}...)

	// println("replace")
	// println(strings.Join(newLines, "\n"))
	// println()

	return
}

var errIndex = 1

func transAPIs(fpath string, lines []string) (newFilePath string, newLines []string) {
	results := linesIterator(lines, func(lines []string, currentIndex int) (
		newLines []string, start, end int) {
		if strings.Contains(lines[currentIndex], "@/api/") {
			return transAPIsImport(lines, currentIndex)
		}

		if strings.Contains(lines[currentIndex], "await ") {
			return transAPIsUse(lines, currentIndex)
		}

		return nil, -1, -1
	})

	return fpath, results
}
