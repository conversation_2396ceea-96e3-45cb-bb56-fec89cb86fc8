export default [
  {
    path: '/',
    component: () => import('kit/pages/contract/index.vue'),
    meta: {
      title: '首页'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/templates',
    component: () => import('kit/pages/contract/templates.vue'),
    meta: {
      title: '模板列表'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/templates/new/step1',
    component: () => import('kit/pages/contract/templatesNewStep1.vue'),
    meta: {
      title: '模板创建',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/templates/:id/step1/edit',
    component: () => import('kit/pages/contract/templatesNewStep1.vue'),
    meta: {
      title: '模板修改',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/templates/:id/step2/edit',
    component: () => import('kit/pages/contract/templatesNewStep2.vue'),
    meta: {
      title: '模板创建',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/rules',
    component: () => import('kit/pages/contract/rules.vue'),
    meta: {
      title: '合同编号管理'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/rules/new',
    component: () => import('kit/pages/contract/rulesNew.vue'),
    meta: {
      title: '新建编号规则'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/rules/:id/edit',
    component: () => import('kit/pages/contract/rulesNew.vue'),
    meta: {
      title: '编辑编号规则'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/types',
    component: () => import('kit/pages/contract/types.vue'),
    meta: {
      title: '合同类型管理'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/types/new',
    component: () => import('kit/pages/contract/typesNew.vue'),
    meta: {
      title: '新建合同类型'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/types/:id/edit',
    component: () => import('kit/pages/contract/typesNew.vue'),
    meta: {
      title: '编辑合同类型'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/types/:id',
    component: () => import('kit/pages/contract/type.vue'),
    meta: {
      title: '查看合同类型'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/approvals',
    component: () => import('kit/pages/contract/approvals.vue'),
    meta: {
      title: '合同流程管理'
    }
  },

  {
    path: '/approvals/new',
    component: () => import('kit/pages/contract/approvalsNew.vue'),
    meta: {
      title: '新建合同流程'
      //   businessCode: "recruit.manage.address",
    }
  },

  {
    path: '/approvals/:id/edit',
    component: () => import('kit/pages/contract/approvalsNew.vue'),
    meta: {
      title: '编辑合同流程'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/approvals/:id',
    component: () => import('kit/pages/contract/approval.vue'),
    meta: {
      title: '查看合同流程'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/contracts',
    component: () => import('kit/pages/contract/contracts.vue'),
    meta: {
      // title: '合同管理123'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/contracts/:id/terminate',
    component: () => import('kit/pages/contract/contractTerminate.vue'),
    meta: {
      title: '解约合同'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/contracts/handleResult',
    component: () => import('kit/pages/contract/contractHandleResult.vue'),
    meta: {
      title: '批量操作结果'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/signings',
    component: () => import('kit/pages/contract/signings.vue'),
    meta: {
      title: '签署任务'
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/signings/drafts/:id/step2/edit',
    component: () => import('kit/pages/contract/signingsDraftsNewStep2.vue'),
    meta: {
      title: '预览填写合同',
      menu: false,
      nav: false
    }
  },
  {
    path: '/signings/drafts/step1/new',
    component: () => import('kit/pages/contract/signingsDraftsNewStep1.vue'),
    meta: {
      title: '发起签署',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/signings/drafts/:id/step1/edit',
    component: () => import('kit/pages/contract/signingsDraftsNewStep1.vue'),
    meta: {
      title: '发起签署',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  // 批量签署-确认文件信息
  {
    path: '/signings/confirm',
    component: () => import('kit/pages/contract/signingsConfirm.vue'),
    meta: {
      title: '确认文件'
    }
  },
  // 批量签署-选择印章
  {
    path: '/signings/sign',
    component: () => import('kit/pages/contract/signingsSign.vue'),
    meta: {
      title: '批量签署'
    }
  },
  {
    path: '/contracts/:id/write',
    component: () => import('kit/pages/contract/contractWrite.vue'),
    meta: {
      title: '填写合同',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/contracts/:id/sign',
    component: () => import('kit/pages/contract/contractSign.vue'),
    meta: {
      title: '签署合同',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/contracts/:id',
    component: () => import('kit/pages/contract/contract.vue'),
    meta: {
      title: '查看合同',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  },
  {
    path: '/navigation',
    component: () => import('kit/pages/contract/navigation.vue'),
    meta: {
      title: '临时页面',
      menu: false,
      nav: false
      //   businessCode: "recruit.manage.address",
    }
  }
]
