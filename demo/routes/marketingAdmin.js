export default [
  // {
  //   path: '/',
  //   redirect: '/introduction'
  // },
  // {
  //   path: '/login',
  //   component: () => import('kit/pages/marketing/admin/login.vue'),
  //   meta: {
  //     title: '登录',
  //     navs: false,
  //     menus: false
  //   }
  // },
  // {
  //   path: '/register',
  //   component: () => import('kit/pages/marketing/admin/register.vue'),
  //   meta: {
  //     title: '注册',
  //     navs: false,
  //     menus: false
  //   }
  // },
  // {
  //   path: '/introduction',
  //   component: () => import('kit/pages/marketing/admin/introduction.vue'),
  //   meta: {
  //     title: '首页引导',
  //     navs: true,
  //     menus: true
  //   }
  // },
  // {
  //   path: '/discount/wechatDiscounts',
  //   component: () =>
  //     import('kit/pages/marketing/admin/discount/wechatDiscounts.vue'),
  //   meta: {
  //     title: '微信立减金',
  //     navs: true,
  //     menus: true
  //   }
  // },
  // {
  //   path: '/discount/wechatDiscountsNew',
  //   component: () =>
  //     import('kit/pages/marketing/admin/discount/wechatDiscountsNew.vue'),
  //   meta: {
  //     title: '新建活动',
  //     navs: true,
  //     menus: true
  //   }
  // },
  // {
  //   path: '/discount/wechatDiscountSentDetail',
  //   component: () =>
  //     import('kit/pages/marketing/admin/discount/wechatDiscountSentDetail.vue'),
  //   meta: {
  //     title: '发放详情',
  //     navs: true,
  //     menus: true
  //   }
  // },
  {
    path: '/activity/wechatActivities',
    component: () =>
      import('kit/pages/marketing/admin/activity/wechatActivities.vue'),
    meta: {
      title: '活动管理',
      navs: true,
      menus: true
    }
  },
  {
    path: '/activity/wechatActivitiesNew',
    component: () =>
      import('kit/pages/marketing/admin/activity/wechatActivitiesNew.vue'),
    meta: {
      title: '新建活动',
      navs: true,
      menus: true
    }
  },
  {
    path: '/activity/wechatActivitySentDetail',
    component: () =>
      import('kit/pages/marketing/admin/activity/wechatActivitySentDetail.vue'),
    meta: {
      title: '发放明细',
      navs: true,
      menus: true
    }
  },
  // {
  //   path: '/organization',
  //   component: () =>
  //     import(
  //       'kit/pages/marketing/admin/enterpriseSetting/organizationStructure.vue'
  //     ),
  //   meta: {
  //     title: '组织架构',
  //     navs: true,
  //     menus: true
  //   }
  // },
  // {
  //   path: '/enterpriseManagement',
  //   component: () =>
  //     import(
  //       'kit/pages/marketing/admin/enterpriseSetting/enterpriseManagement.vue'
  //     ),
  //   meta: {
  //     title: '企业管理',
  //     navs: true,
  //     menus: true
  //   }
  // },
  {
    path: '/test',
    component: () => import('kit/pages/marketing/admin/test.vue'),
    meta: {
      title: '企业管理',
      navs: true,
      menus: true
    }
  }
]
