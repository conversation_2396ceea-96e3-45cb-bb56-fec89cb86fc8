import EmployeePayrolls from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/employeePayrolls.vue?t=1697699219284'
import EmployeePayroll from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/employeePayroll.vue?t=1697699219284'
import LoginPhoneBoHai from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/login.vue?t=1697764376763'
import WorkbenchBoHai from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/workBench.vue?t=1697768775024'
import Mine from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/mine.vue?t=1697699219042'
import PhoneChange from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/phoneChange.vue?t=1697699219284'
import SecurityPasswordChange from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/securityPasswordChange.vue?t=1697699219284'
import SecurityPasswordForgetChange from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/securityPasswordForgetChange.vue?t=1697699219284'
import SecurityPasswordNew from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/securityPasswordNew.vue?t=1697699219284'
import Ocr from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/auth/ocr.vue?t=1697699218808'
import LivingBody from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/auth/livingBody.vue?t=1697699218807'
import AuthMiddlePage from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/authMiddlePage.vue?t=1697699219284'
import Signatures from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/signatures.vue'
import ExpertConsultQuestions from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/expertConsultQuestions.vue'
import ExpertConsultQuestion from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/ExpertConsultQuestion.vue'
import ElectronicHistoryFile from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/electronicHistoryFile.vue'
import Setting from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/setting.vue'
import EmailNew from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/emailNew.vue'
import EmailChange from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/emailChange.vue'
import EmailForgetChange from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/emailForgetChange.vue'
import Todos from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/todos.vue'
import Contracts from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/contracts.vue'
import Contract from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/contract.vue'
import ContractDetail from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/contractDetail.vue'
import Messages from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/messages.vue'
import Message from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/message.vue'

import Attend from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/attend.vue?t=1697699219284'
import AttendOutSide from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/attendOutSide.vue?t=1697699219284'
import AttendRule from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/attendRule.vue'
import AttendApprovals from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/attendApprovals.vue'
import AttendStats from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/attendStats.vue?t=1697699219284'
import AttendStatsMore from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/attendStatsMore.vue'
import Welfares from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/welfares.vue?t=1697699219284'

import MyAgreement from '/tj/hrsaas/mph5/@fs/D:/front/hrs_kit-front/src/pages/mpH5/myAgreement.vue'

export default [
  {
    path: '/employees/payrolls',
    component: EmployeePayrolls,
    meta: {
      title: '工资条'
    }
  },
  {
    path: '/employees/payroll',
    component: EmployeePayroll,
    meta: {
      title: '工资条详情'
    }
  },
  {
    path: '/login',
    component: LoginPhoneBoHai,
    meta: {
      title: '登录'
    }
  },
  {
    path: '/workbench',
    component: WorkbenchBoHai,
    meta: {
      title: '渤海银行工作台'
    }
  },
  {
    path: '/mine',
    component: Mine,
    meta: {
      title: '我的'
    }
  },
  {
    path: '/phoneChange',
    component: PhoneChange,
    meta: {
      title: '修改手机号'
    }
  },
  {
    path: '/securityPasswordChange',
    component: SecurityPasswordChange,
    meta: {
      title: '修改安全密码'
    }
  },
  {
    path: '/securityPasswordForgetChange',
    component: SecurityPasswordForgetChange,
    meta: {
      title: '忘记安全密码'
    }
  },
  {
    path: '/securityPasswordNew',
    component: SecurityPasswordNew,
    meta: {
      title: '设置安全密码'
    }
  },
  {
    path: '/ocr',
    component: Ocr,
    meta: {
      title: '实体认证'
    }
  },
  {
    path: '/livingBody',
    component: LivingBody,
    meta: {
      title: '活体检测'
    }
  },
  {
    path: '/authentication',
    component: AuthMiddlePage,
    meta: {
      title: '鉴权中间页'
    }
  },
  {
    path: '/attend',
    component: Attend,
    meta: {
      title: '考勤打卡'
    }
  },
  {
    path: '/attendOutSide',
    component: AttendOutSide,
    meta: {
      title: '外勤打卡'
    }
  },
  {
    path: '/attendRule',
    component: AttendRule,
    meta: {
      title: '考勤规则'
    }
  },
  {
    path: '/attendApprovals',
    component: AttendApprovals,
    meta: {
      title: '申请'
    }
  },
  {
    path: '/attendStats',
    component: AttendStats,
    meta: {
      title: '统计'
    }
  },
  {
    path: '/attendStatsMore',
    component: AttendStatsMore,
    meta: {
      title: '月统计'
    }
  },
  {
    path: '/signatures',
    component: Signatures,
    meta: {
      title: '我的签名'
    }
  },
  {
    path: '/expertConsultQuestions',
    component: ExpertConsultQuestions,
    meta: {
      title: '专家咨询'
    }
  },
  {
    path: '/expertConsultQuestion',
    component: ExpertConsultQuestion,
    meta: {
      title: '专家咨询详情'
    }
  },
  {
    path: '/electronicHistoryFile',
    component: ElectronicHistoryFile,
    meta: {
      title: '电子历史档案'
    }
  },
  {
    path: '/setting',
    component: Setting,
    meta: {
      title: '设置'
    }
  },
  {
    path: '/emailNew',
    component: EmailNew,
    meta: {
      title: '设置邮箱'
    }
  },
  {
    path: '/emailChange',
    component: EmailChange,
    meta: {
      title: '修改邮箱'
    }
  },
  {
    path: '/emailForgetChange',
    component: EmailForgetChange,
    meta: {
      title: '忘记邮箱'
    }
  },
  {
    path: '/todos',
    component: Todos,
    meta: {
      title: '待办'
    }
  },
  {
    path: '/contracts',
    component: Contracts,
    meta: {
      title: '合同列表'
    }
  },
  {
    path: '/contracts/:id/:stepId',
    component: Contract,
    meta: {
      title: '查看合同'
    }
  },
  {
    path: '/contractDetail/:id/:stepId',
    component: ContractDetail,
    meta: {
      title: '查看合同详情'
    }
  },
  {
    path: '/messages',
    component: Messages,
    meta: {
      title: '消息通知'
    }
  },
  {
    path: '/message/:id/:noticeType',
    component: Message,
    meta: {
      title: '消息详情'
    }
  },
  {
    path: '/welfares',
    component: Welfares,
    meta: {
      title: '员工福利'
    }
  },
  {
    path: '/myAgreement',
    component: MyAgreement,
    meta: {
      title: '我的协议'
    }
  }
]
