<template>
  <MultipleEmployeeInsideDepartment
    :title="'授权用户'"
    :departments="departments"
    :employees="employees"
    :selectedEmployees="selectedEmployees"
    @select="select"
    @unselect="unselect"
    @selectAll="selectAll"
    @unselectAll="unselectAll"
    @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
    @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
    @confirm="confirm"
    @cancel="cancel"
    @search="search"
    @clear="clear"
  />
</template>

<script>
import MultipleEmployeeInsideDepartment from 'kit/components/ui/picker/multipleEmployeeInsideDepartment.vue'
import testDepartments from './testDepartments'
import testEmployees from './testEmployees'
export default {
  components: {
    MultipleEmployeeInsideDepartment
  },
  data() {
    return {
      departments: testDepartments,
      employees: testEmployees,
      selectedEmployees: []
    }
  },
  methods: {
    confirm() {
      console.log('confirm', this.selectedEmployee)
    },
    cancel() {
      this.selectedEmployee = null
      console.log('cancel', this.selectedEmployee)
    },
    handleClickDepartmentSubdivision(v) {
      this.departments = v.children
    },
    handleClickBreadcrumbDepartment(v) {
      this.departments = testDepartments
    },
    search(v) {
      console.log('search', v)
    },
    unselect(v) {
      this.selectedEmployees = this.selectedEmployees.filter(
        item => item.id !== v.id
      )
    },
    select(v) {
      const n = [...this.selectedEmployees]
      n.push(v)
      this.selectedEmployees = n
    },
    selectAll() {
      this.selectedEmployees = this.employees.filter(item => !item.disabled)
    },
    unselectAll() {
      this.selectedEmployees = []
    },
    clear() {
      this.selectedEmployees = []
    }
  }
}
</script>

<style scoped>
</style>