<template>
  <SingleDepartment
    :departments="departments"
    :selectedDepartment="selectedDepartment"
    @select="select"
    @unselect="unselect"
    @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
    @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
    @confirm="confirm"
    @cancel="cancel"
    @search="search"
  />
</template>

<script>
import SingleDepartment from 'kit/components/ui/picker/department/single.vue'
import testDepartments from './testDepartments'
export default {
  components: {
    SingleDepartment
  },
  data() {
    return {
      departments: testDepartments,
      selectedDepartment: null
    }
  },
  methods: {
    confirm() {
      console.log('confirm', this.selectedDepartment)
    },
    cancel() {
      this.selectedDepartment = null
      console.log('cancel', this.selectedDepartment)
    },
    handleClickDepartmentSubdivision(v) {
      this.departments = v.children
    },
    handleClickBreadcrumbDepartment(v) {
      this.departments = testDepartments
    },
    search(v) {
      console.log('search', v)
    },
    unselect(v) {
      this.selectedDepartment = null
    },
    select(v) {
      this.selectedDepartment = v
    }
  }
}
</script>

<style scoped>
</style>