<template>
  <SingleEmployeeInsideDepartment
    :title="'部门下单选员工'"
    :departments="departments"
    :employees="employees"
    :selectedEmployee="selectedEmployee"
    @select="select"
    @unselect="unselect"
    @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
    @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
    @confirm="confirm"
    @cancel="cancel"
    @search="search"
  />
</template>

<script>
import SingleEmployeeInsideDepartment from 'kit/components/ui/picker/singleEmployeeInsideDepartment.vue'
import testDepartments from './testDepartments'
import testEmployees from './testEmployees'
export default {
  components: {
    SingleEmployeeInsideDepartment
  },
  data() {
    return {
      departments: testDepartments,
      employees: testEmployees,
      selectedEmployee: null
    }
  },
  methods: {
    confirm() {
      console.log('confirm', this.selectedEmployee)
    },
    cancel() {
      this.selectedEmployee = null
      console.log('cancel', this.selectedEmployee)
    },
    handleClickDepartmentSubdivision(v) {
      this.departments = v.children
    },
    handleClickBreadcrumbDepartment(v) {
      this.departments = testDepartments
    },
    search(v) {
      console.log('search', v)
    },
    unselect(v) {
      this.selectedEmployee = null
    },
    select(v) {
      this.selectedEmployee = v
    }
  }
}
</script>

<style scoped>
</style>