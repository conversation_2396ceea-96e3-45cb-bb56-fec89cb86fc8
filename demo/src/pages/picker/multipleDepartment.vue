<template>
  <MultipleDepartment
    :departments="departments"
    :selectedDepartments="selectedDepartments"
    @select="select"
    @unselect="unselect"
    @selectAll="selectAll"
    @unselectAll="unselectAll"
    @clickDepartmentSubdivision="handleClickDepartmentSubdivision"
    @clickBreadcrumbDepartment="handleClickBreadcrumbDepartment"
    @confirm="confirm"
    @cancel="cancel"
    @search="search"
    @clear="clear"
  />
</template>

<script>
import MultipleDepartment from 'kit/components/ui/picker/department/multiple.vue'
import testDepartments from './testDepartments'
export default {
  components: {
    MultipleDepartment
  },
  data() {
    return {
      departments: testDepartments,
      selectedDepartments: []
    }
  },
  methods: {
    confirm() {
      console.log('confirm', this.selectedDepartment)
    },
    cancel() {
      this.selectedDepartment = null
      console.log('cancel', this.selectedDepartment)
    },
    handleClickDepartmentSubdivision(v) {
      this.departments = v.children
    },
    handleClickBreadcrumbDepartment(v) {
      this.departments = testDepartments
    },
    search(v) {
      console.log('search', v)
    },
    unselect(v) {
      console.log('unselect', v)
      this.selectedDepartments = this.selectedDepartments.filter(
        item => item.id !== v.id
      )
    },
    select(v) {
      const n = [...this.selectedDepartments]
      n.push(v)
      this.selectedDepartments = n
    },
    selectAll() {
      this.selectedDepartments = this.departments.filter(item => !item.disabled)
    },
    unselectAll() {
      this.selectedDepartments = []
    },
    clear() {
      this.selectedDepartments = []
    }
  }
}
</script>

<style scoped>
</style>