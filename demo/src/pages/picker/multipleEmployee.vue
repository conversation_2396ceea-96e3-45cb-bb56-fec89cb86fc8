<template>
  <MultipleEmployee
    :employees="employees"
    :selectedEmployees="selectedEmployees"
    @select="select"
    @unselect="unselect"
    @selectAll="selectAll"
    @unselectAll="unselectAll"
    @confirm="confirm"
    @cancel="cancel"
    @search="search"
    @clear="clear"
  />
</template>

<script>
import MultipleEmployee from 'kit/components/ui/picker/employee/multiple.vue'
import testEmployees from './testEmployees'
export default {
  components: {
    MultipleEmployee
  },
  data() {
    return {
      employees: testEmployees,
      selectedEmployees: []
    }
  },
  methods: {
    confirm() {
      console.log('confirm', this.selectedEmployee)
    },
    cancel() {
      this.selectedEmployee = null
      console.log('cancel', this.selectedEmployee)
    },
    search(v) {
      console.log('search', v)
    },
    unselect(v) {
      console.log('unselect', v)
      this.selectedEmployees = this.selectedEmployees.filter(
        item => item.id !== v.id
      )
    },
    select(v) {
      const n = [...this.selectedEmployees]
      n.push(v)
      this.selectedEmployees = n
    },
    selectAll() {
      this.selectedEmployees = this.employees.filter(item => !item.disabled)
    },
    unselectAll() {
      this.selectedEmployees = []
    },
    clear() {
      this.selectedEmployees = []
    }
  }
}
</script>
