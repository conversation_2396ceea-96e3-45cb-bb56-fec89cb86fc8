import { isObject, isString } from './index'
import i18ns from './i18ns'
import toLoginPage from './toLoginPage'
import { removeToken } from './token'
import { showMessage } from './showMessage'

// 防止重复跳转的标志
let isRedirecting = false

const handleError = (err, tokenKey) => {
  if (err && err.errorCode === 2) {
    // 防止重复跳转
    if (isRedirecting) {
      return
    }

    isRedirecting = true
    showMessage(err.message || '您的登录已过期，请重新登录', 'error')
    removeToken(tokenKey)

    setTimeout(() => {
      toLoginPage()
      setTimeout(() => {
        isRedirecting = false
      }, 3000)
    }, 1000)
    return
  }

  //重复请求不报错
  if (err && err.message === 'duplicated request') {
    console.warn(err)
    return
  }

  var msg = err
  if (isObject(err) && isString(err.message)) {
    msg = err.message
  }

  if (i18ns(msg)) {
    msg = i18ns(msg)
  }

  showMessage(msg, 'error')
}

export default handleError
