import App from './app.vue'
import Vue from 'vue'
import VueRouter from 'vue-router'
import routes from './routes'
import 'vant/lib/index.css'

Vue.use(VueRouter)

const router = new VueRouter({
  mode: 'history',
  base: '/task/',
  routes
})

router.beforeEach((to, from, next) => {
  document.title = to.meta.title
  next()
})

const vStore = null //为了避免类型检查
new Vue({
  el: '#app',
  router,
  vStore,
  render: h => h(App)
}).$mount()
