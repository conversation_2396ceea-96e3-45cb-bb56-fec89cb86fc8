import Home from './app.vue'
import Login from 'kit/pages/operateLabor/task/login.vue'
import OcrIdentify from 'kit/pages/operateLabor/task/ocrIdentify.vue'
import FaceAuth from 'kit/pages/operateLabor/task/faceAuth.vue'
import Ocr from 'kit/pages/operateLabor/task/ocr.vue'
import LaborContract from 'kit/pages/operateLabor/task/laborContract.vue'
import LaborContractSign from 'kit/pages/operateLabor/task/laborContractSign.vue'
import Agreement from 'kit/pages/operateLabor/task/agreement.vue'

const routes = [
  {
    path: '/',
    component: Home
  },
  {
    path: '/login',
    component: Login
  },
  {
    path: '/ocr',
    component: Ocr
  },
  {
    path: '/ocrIdentify',
    component: OcrIdentify
  },
  {
    path: '/faceAuth',
    component: FaceAuth
  },
  {
    path: '/laborContract',
    component: LaborContract
  },
  {
    path: '/laborContractSign',
    component: LaborContractSign
  },
  {
    path: '/agreement',
    component: Agreement,
    meta: {
      title: '服务协议'
    }
  },
]

export default routes
