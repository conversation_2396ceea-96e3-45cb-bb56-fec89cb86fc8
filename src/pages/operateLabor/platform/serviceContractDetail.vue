<template>
  <div v-loading="loading">
    <div style="padding: 10px">
      <el-form :model="contractData" label-width="120px" style="width: 1000px; margin-left: 50px;">

        <Title title="基本信息" />

        <el-row :gutter="40">
         <el-col :span="12">
            <el-form-item label="合同名称">
              <el-input v-model="contractData.name" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户">
              <el-input v-model="contractData.customerName" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="作业主体">
              <el-input v-model="contractData.supplierName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="合同有效期" v-if="!contractData.timeFixed">
              <el-input :value="formatDateRange(contractData.startDate, contractData.endDate)" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第三行：合同期限 和 合同有效期 -->
        <el-row :gutter="40">
          <el-col v-if="false" :span="12">
            <el-form-item label="合同期限">
              <el-input :value="contractData.timeFixed ? '固定期限' : '无固定期限'" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 第四行：操作角色 -->
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item label="操作角色">
              <el-tag v-if="contractData.roleIds && contractData.roleIds.length > 0" v-for="roleId in contractData.roleIds" :key="roleId" type="primary" size="small"
                style="margin-right: 8px; margin-bottom: 4px;">
                {{ getRoleName(roleId) }}
              </el-tag>
              <span v-if="!contractData.roleIds || contractData.roleIds.length === 0" style="color: #999;">暂无角色</span>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 合同附件 -->
        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item label="合同附件">
              <FileList :fileIds="contractData.fileIds" />
            </el-form-item>
          </el-col>
        </el-row>

        <Title title="发票信息" />

        <!-- 发票信息第一行 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="抬头">
              <el-input v-model="contractData.invoiceTitle" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="纳税人识别号">
              <el-input v-model="contractData.invoiceTaxNo" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 发票信息第二行 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="开户行">
              <el-input v-model="contractData.invoiceBankName" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="银行账号">
              <el-input v-model="contractData.invoiceBankAccount" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 发票信息第三行 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="注册地址">
              <el-input v-model="contractData.invoiceRegisterAddress" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="企业电话">
              <el-input v-model="contractData.invoiceCompanyTel" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 发票信息第四行 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="发票备注">
              <el-input v-model="contractData.invoiceRemark" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <Title title="管理费计算" />

        <!-- 管理费计算第一行 -->
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="计算规则">
              <el-input :value="getCalculationRuleText(contractData.manageCalculationRule)" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="contractData.manageCalculationRule === 'EMPLOYEE_COUNT'">
            <el-form-item label="服务费金额">
              <el-input :value="contractData.manageAmount ? `${contractData.manageAmount}元` : ''" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="contractData.manageCalculationRule === 'PAYABLE_AMOUNT_RATE'">
            <el-form-item label="费率">
              <el-input :value="contractData.manageRate ? `${contractData.manageRate}%` : ''" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 提前中止信息 -->
        <div v-if="contractData.stopped">
          <Title title="提前中止信息" />
          <el-row :gutter="40">
            <el-col :span="12">
              <el-form-item label="中止时间">
                <el-input :value="formatDateTime(contractData.stopTime)" readonly></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="中止原因">
                <el-input v-model="contractData.stopReason" readonly></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <!-- 操作按钮 -->
        <el-row :gutter="40" style="margin-top: 40px;">
          <el-col :span="24" style="text-align: center;">
            <el-button @click="onCancel">返回</el-button>
          </el-col>
        </el-row>

      </el-form>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import FileList from './components/fileList.vue'

const client = makeClient()

export default {
  components: { Title, FileList },
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 1000,
        sorts: [
        ],
        withTotal: false,
        withDisabled: false,
        withDeleted: false,
        filters: {
          name: ''
        }
      },
      loading: true,
      contractId: null,
      contractData: {},
      roleOptions: [],
    }
  },

  async created() {
    // 获取路由参数中的合同ID
    this.contractId = this.$route.params.id

    // 加载数据
    await this.loadRoleOptions()
    await this.loadContractData()
  },

  methods: {
    // 加载角色选项
    async loadRoleOptions() {
      try {
        const [err, response] = await client.listRoles({
          body: this.conditions
        })

        if (response.success && response.data) {
          this.roleOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载角色选项失败：', error)
      }
    },

    // 加载合同数据
    async loadContractData() {
      try {
        const [err, response] = await client.queryContract({
          body: { id: this.contractId }
        })

        if(err){
          this.$message.error(err.message)
          this.loading = false
          return
        }
        if (response.success) {
          this.contractData = response.data || {}
        } else {
          this.$message.error(response.message || '获取合同信息失败')
        }
        this.loading = false
      } catch (error) {
        handleError(error)
        this.loading = false
      }
    },

    // 获取角色名称
    getRoleName(roleId) {
      const role = this.roleOptions.find(r => r.id === roleId)
      return role ? role.name : `角色${roleId}`
    },

    // 获取计算规则文本
    getCalculationRuleText(rule) {
      const ruleMap = {
        'EMPLOYEE_COUNT': '按人数计算',
        'PAYABLE_AMOUNT_RATE': '按应付金额比例计算'
      }
      return ruleMap[rule] || rule
    },

    // 格式化日期时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      return new Date(dateTime).toLocaleString('zh-CN')
    },

    // 格式化日期范围
    formatDateRange(startDate, endDate) {
      if (!startDate || !endDate) return ''
      return `${startDate} 至 ${endDate}`
    },

    // 取消/返回
    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped></style>
