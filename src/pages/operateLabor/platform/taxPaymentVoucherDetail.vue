<template>
  <div
    class="taxPaymentVoucherDetail"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <div>
      <div style="background: white; padding: 10px; margin-bottom: 20px;">
        <h1 style="font-size: 25px; font-weight: 600; color: #262626; margin: 0 0 12px 0; line-height: 1.2;">
          {{ voucherData.supplierCorporationName }}
        </h1>
        <p style="font-size: 14px; color: #8c8c8c; margin: 0;">
          税款所属期：{{ voucherData.taxPaymentPeriod }}
        </p>
    </div>

      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="基本信息" name="detail">
          <div v-loading="loading" style="padding: 20px 0">
            <el-form
              :model="voucherData"
              label-width="120px"
              style="max-width: 800px"
            >
              <!-- <el-row :gutter="40">
                <el-col :span="12">
                  <el-form-item label="完税证明ID">
                    <el-input
                      v-model="voucherData.id"
                      readonly
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="作业主体名称">
                    <el-input
                      v-model="voucherData.supplierCorporationName"
                      readonly
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="40">
                <el-col :span="12">
                  <el-form-item label="税款所属期">
                    <el-input
                      v-model="voucherData.taxPaymentPeriod"
                      readonly
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="上传时间">
                    <el-input
                      v-model="voucherData.createTime"
                      readonly
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="40">
                <el-col :span="12">
                  <el-form-item label="最后修改时间">
                    <el-input
                      v-model="voucherData.modifyTime"
                      readonly
                      style="width: 300px"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row> -->

              <!-- 完税证明附件 -->
              <el-row :gutter="40">
                <el-col :span="24">
                  <el-form-item label="完税证明附件">
                    <FileList :fileIds="voucherData.fileIds" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import FileList from './components/fileList.vue'

const client = makeClient()

export default {
  components: {
    FileList
  },
  data() {
    return {
      activeTab: 'detail',
      loading: true,
      voucherData: {
        id: '',
        supplierCorporationId: '',
        taxPaymentPeriod: '',
        fileIds: '',
        supplierCorporationName: '',
        createTime: '',
        modifyTime: '',
        supplierId: ''
      }
    }
  },
  async created() {
    const id = this.$route.params.id
    if (id) {
      await this.loadVoucherDetail(id)
    }
  },
  methods: {
    async loadVoucherDetail(id) {
      this.loading = true
      try {
        const [err, response] = await client.queryTaxPaymentVoucher({
          body: { id: parseInt(id) }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success && response.data) {
          this.voucherData = response.data
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.taxPaymentVoucherDetail {
  padding: 0;
}

/* Tab样式调整 */
::v-deep .el-tabs__header {
  margin: 0 0 20px 0;
}

::v-deep .el-tabs__content {
  padding: 0;
}

::v-deep .el-tab-pane {
  padding: 0;
}

/* 表单样式调整 */
::v-deep .el-form-item__label {
  color: #606266;
  font-weight: 500;
}

::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #606266;
}
</style>
