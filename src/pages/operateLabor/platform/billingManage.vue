<template>
  <div
    class="corporations"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="110px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户">
            <el-select
              filterable
              v-model="conditions.filters.customerIds"
              placeholder="请选择所属客户"
              style="width: 280px"
              clearable
              multiple
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="客户">
            <el-select
              filterable
              v-model="conditions.filters.customerIds"
              placeholder="请选择所属客户"
              style="width: 280px"
              clearable
              multiple
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <!-- <el-form-item label="账单月">
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleCreateTimeChange"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item> -->
          <el-form-item label="服务合同">
            <el-select
              filterable
              v-model="conditions.filters.contractId"
              placeholder="请选择所属服务合同"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in contractOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="conditions.filters.billStatus"
              placeholder="请选择状态"
              style="width: 280px"
              clearable
            >
              <el-option label="生成中" value="GENERATING"></el-option>
              <el-option label="已生成" value="GENERATED"></el-option>
              <el-option label="待确认" value="PENDING_CONFIRM"></el-option>
              <el-option label="已确认" value="CONFIRMED"></el-option>
            </el-select>
          </el-form-item>
           <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <div>
          <!-- <el-form-item label="状态">
            <el-select
              v-model="conditions.filters.billStatus"
              placeholder="请选择状态"
              style="width: 280px"
              clearable
            >
              <el-option label="生成中" value="GENERATING"></el-option>
              <el-option label="已生成" value="GENERATED"></el-option>
              <el-option label="待确认" value="PENDING_CONFIRM"></el-option>
              <el-option label="已确认" value="CONFIRMED"></el-option>
            </el-select>
          </el-form-item> -->
         
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        生成账单
      </el-button>
    </div>

    <el-table
      v-loading="loading"
      size="small"
      :data="tableData"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="结算账单ID"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="customerName"
        label="客户名称"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="contractName"
        label="服务合同名称"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="supplierCorporationName"
        label="作业主体名称"
        width="160"
      ></el-table-column>
      <el-table-column
        prop="totalReceivableAmount"
        label="总费用"
        width="160"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="生成时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column
        prop="confirmTime"
        label="确认时间"
        width="160"
        :formatter="formatDateTime"
      ></el-table-column>
      <el-table-column label="状态" width="100">
        <template slot-scope="scope">
          <el-tag
            :type="scope.row.billStatus ? 'success' : 'danger'"
            size="small"
          >
            {{ formatterStatus(scope.row.billStatus) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" min-width="280">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)">
            查看
          </el-button>
          <el-button type="text" size="small" @click="handleSend(scope.row)">
            发送甲方确认
          </el-button>
          <el-button
            v-if="scope.row.billStatus === 'PENDING_CONFIRM'"
            type="text"
            size="small"
            @click="handleConfirm(scope.row)"
          >
            确认
          </el-button>
          <el-button type="text" size="small" @click="handleDelete(scope.row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <el-dialog
      title="生成账单"
      :visible.sync="dialogVisible"
      width="560px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="100px"
      >
        <el-form-item prop="contractId" label="服务合同">
          <el-select
            filterable
            v-model="ruleForm.contractId"
            placeholder="请选择所属服务合同"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="item in contractOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="billMonth" label="账单月">
          <el-date-picker
            style="width: 100%"
            v-model="ruleForm.billMonth"
            type="month"
            placeholder="请选择账单月"
            value-format="yyyy-MM-01"
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import SupplierCustomersSelector from './selector/supplierCustomers.vue'
import handleError from '../../../helpers/handleError'
import handleSuccess from '../../../helpers/handleSuccess'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  components: {
    SupplierCustomersSelector
  },
  data() {
    return {
      fullShown: false,
      createTimeRange: [],
      conditions: {
        start: 1,
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          supplierCorporationId: '',
          customerIds: [],
          contractId: '',
          billMonthStart: '',
          billMonthEnd: '',
          billStatus: null
        }
      },
      total: 0,
      tableData: [],
      ruleForm: {
        contractId: '',
        billMonth: ''
      },
      rules: {
        contractId: [
          { required: true, message: '请选择服务合同', trigger: 'change' }
        ],
        billMonth: [
          { required: true, message: '请选择账单月', trigger: 'change' }
        ]
      },
      contracts: [],
      loading: true,
      dialogVisible: false,
      supplierOptions: [],
      customerOptions: [],
      contractOptions: []
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.loadCustomerOptions()
    await this.loadContractOptions()
    await this.getList()
  },
  methods: {
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },

    // 加载客户选项
    async loadCustomerOptions() {
      const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
        const [err, response] = await client.supplierListCustomer({
          body: { filters: conditions.filters }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },

    // 加载合同选项
    async loadContractOptions() {
      try {
        const [err, response] = await client.supplierListContract({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.contractOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载合同选项失败：', error)
      }
    },

    formatterStatus(value) {
      switch (value) {
        case 'GENERATING':
          return '生成中'
        case 'GENERATED':
          return '已生成'
        case 'PENDING_CONFIRM':
          return '待确认'
        case 'CONFIRMED':
          return '已确认'
        default:
          return '-'
      }
    },
    onSearch() {
      this.conditions.offset = 0
      if (this.createTimeRange.length) {
        this.conditions.filters.billMonthStart = this.createTimeRange[0]
        this.conditions.filters.billMonthStart = this.createTimeRange[0]
      }
      this.getList()
    },

    onReset() {
      this.conditions.filters = {
        supplierCorporationId: '',
        customerIds: [],
        contractId: '',
        billMonthStart: '',
        billMonthEnd: '',
        billStatus: null
      }
      this.onSearch()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.apiSupplierBillsList({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.tableData = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.billMonthStart = value[0]
        this.conditions.filters.billMonthEnd = value[1]
      } else {
        this.conditions.filters.billMonthStart = null
        this.conditions.filters.billMonthEnd = null
      }
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    formatDateTime(row, column, cellValue) {
      if (!cellValue) return ''
      return new Date(cellValue).toLocaleString('zh-CN')
    },

    handleAdd() {
      this.dialogVisible = true
    },

    handleView(row) {
      this.$router.push(`/billingManage/${row.id}`)
    },

    async handleSend(row) {
      const [err] = await client.apiSupplierBillsSubmit({
        body: {
          billId: row.id
        }
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('操作成功')
      this.getList()
    },
    async handleConfirm(row) {
      const [err] = await client.apiSupplierBillsConfirm({
        body: {
          billId: row.id
        }
      })

      if (err) {
        handleError(err)
        return
      }

      handleSuccess('确认成功')
      this.getList()
    },

    handleDelete(row) {
      this.$confirm('此操作将永久删除该账单, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const [err] = await client.apiSupplierBillsDelete({
            body: {
              billId: row.id
            }
          })

          if (err) {
            handleError(err)
            return
          }

          handleSuccess('删除成功')
          // 刷新列表
          await this.getList()
        } catch (error) {
          handleError(error)
        }
      })
    },

    async confirm() {
      await this.$refs.ruleForm.validate()
      const [err, r] = await client.apiSupplierBillsGenerate({
        body: this.ruleForm
      })
      if (err) {
        handleError(err)
        return
      }
      this.$message.success('账单生成成功')
      this.dialogVisible = false
      this.getList()
    }
  }
}
</script>
