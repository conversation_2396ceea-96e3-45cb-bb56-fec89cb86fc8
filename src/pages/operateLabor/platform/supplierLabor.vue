<template>
  <div
    class="supplier-labor"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="姓名">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入姓名"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input
              v-model="conditions.filters.cellPhone"
              placeholder="请输入手机号"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-button type="text" @click="fullShown = true">展开</el-button>
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="姓名">
            <el-input
              v-model="conditions.filters.name"
              placeholder="请输入姓名"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input
              v-model="conditions.filters.cellPhone"
              placeholder="请输入手机号"
              style="width: 280px"
            ></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="证件号码">
            <el-input
              v-model="conditions.filters.idCard"
              placeholder="请输入证件号码"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.supplierId"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="客户">
            <el-select
              filterable
              v-model="conditions.filters.customerId"
              placeholder="请选择所属客户"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="服务合同">
            <el-select
              filterable
              v-model="conditions.filters.contractId"
              placeholder="请选择所属服务合同"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="item in contractOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="人员状态">
            <el-select
              v-model="conditions.filters.empStatus"
              placeholder="请选择人员状态"
              style="width: 280px"
              clearable
            >
              <el-option
                v-for="(label, value) in empStatusOptions"
                :key="value"
                :label="label"
                :value="value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">
        <i class="el-icon-plus" />
        添加
      </el-button>
      <el-button type="default" @click="handleBatchImport">
        <i class="el-icon-upload2" />
        批量导入
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="name"
        label="姓名"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="idCard"
        label="证件号码"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="cellphone"
        label="手机号码"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="signStatus" label="签约状态" width="100">
        <template slot-scope="scope">
          <span
            :class="[
              'status-tag',
              scope.row.signStatus ? 'status-signed' : 'status-unsigned'
            ]"
          >
            {{ scope.row.signStatus ? '已签约' : '未签约' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="corporationName"
        label="所属作业主体"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="customerName"
        label="所属客户"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="contractName"
        label="所属服务合同"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column prop="empStatus" width="100" label="人员状态">
        <template slot-scope="scope">
          <span :class="['status-tag', getEmpStatusClass(scope.row.empStatus)]">
            {{ getEmpStatusText(scope.row.empStatus) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        width="150"
        label="创建时间"
      ></el-table-column>
      <el-table-column
        prop="modifyTime"
        width="150"
        label="更新时间"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="200">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >详情</el-button
          >
          <el-button type="text" size="small" @click="handleResign(scope.row)"
            >离职</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleInitiateContract(scope.row)"
            >发起合同签署</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>

    <!-- 批量导入对话框 -->
    <BatchImportDialog ref="batchImportDialog" @refresh="getList" />
  </div>
</template>

<script>
import makeClient from '../../../services/operateLabor/makeClient'
import handleError from '../../../helpers/handleError'
import BatchImportDialog from './components/batchImportDialog.vue'

const client = makeClient()

export default {
  components: {
    BatchImportDialog
  },
  data() {
    return {
      fullShown: false,
      conditions: {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          cellPhone: '',
          idCard: '',
          supplierId: '',
          customerId: '',
          contractId: '',
          empStatus: ''
        }
      },
      total: 0,
      data: [],
      loading: true,
      // 选项数据
      supplierOptions: [],
      customerOptions: [],
      contractOptions: [],
      empStatusOptions: {
        ON_THE_JOB: '在职',
        QUIT: '离职'
      }
    }
  },
  async created() {
    await this.loadOptions()
    await this.getList()
  },
  methods: {
    async loadOptions() {
      await this.loadSupplierOptions()
      await this.loadCustomerOptions()
      await this.loadContractOptions()
    },

    // 加载供应商选项（作业主体）
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },

    // 加载客户选项
    async loadCustomerOptions() {
      const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
        const [err, response] = await client.supplierListCustomer({
          body: { filters: conditions.filters }
        })

        if (response && response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },

    // 加载合同选项
    async loadContractOptions() {
      try {
        const [err, response] = await client.supplierListContract({
          body: { filters: {} }
        })

        if (response && response.success && response.data) {
          this.contractOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载合同选项失败：', error)
      }
    },

    getEmpStatusText(status) {
      return this.empStatusOptions[status] || status
    },

    getEmpStatusClass(status) {
      const classMap = {
        ON_THE_JOB: 'status-ongoing',
        QUIT: 'status-stopped'
      }
      return classMap[status] || 'status-default'
    },

    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },

    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        sorts: [],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: '',
          cellPhone: '',
          idCard: '',
          supplierId: '',
          customerId: '',
          contractId: '',
          empStatus: ''
        }
      }
      this.getList()
    },

    async getList() {
      this.loading = true

      const [err, r] = await client.supplierLaborList({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },

    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },

    handleAdd() {
      // 跳转到添加页面
      this.$router.push('/supplierLabor/new')
    },

    handleBatchImport() {
      // 打开批量导入对话框
      this.$refs.batchImportDialog.open()
    },

    handleView(row) {
      // 跳转到详情页面
      this.$router.push(`/supplierLabor/${row.id}`)
    },

    handleResign(row) {
      // 处理离职操作
      this.$message.info('离职功能开发中')
    },

    handleInitiateContract(row) {
      // 处理发起合同签署操作
      this.$message.info('发起合同签署功能开发中')
    }
  }
}
</script>

<style scoped>
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-signed {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-unsigned {
  background-color: #f5f5f5;
  color: #666666;
  border: 1px solid #d9d9d9;
}

.status-ongoing {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-stopped {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-negotiation {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>
