<template>
  <div
    class="invoices-container"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <!-- 搜索区域 -->
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="100px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <el-form-item label="发票申请编号">
            <el-input
              v-model="conditions.filters.sn"
              placeholder="请输入发票申请编号"
              style="width: 300px"
            ></el-input>
          </el-form-item>
          <el-form-item label="所属客户">
            <el-select
              v-model="conditions.filters.customerId"
              placeholder="请选择所属客户"
              style="width: 280px"
              clearable
              filterable
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>
        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <el-form-item label="发票申请编号">
            <el-input
              v-model="conditions.filters.sn"
              placeholder="请输入发票申请编号"
              style="width: 280px"
            ></el-input>
          </el-form-item>
          <el-form-item label="所属客户">
            <el-select
              v-model="conditions.filters.customerId"
              placeholder="请选择所属客户"
              style="width: 280px"
              clearable
              filterable
            >
              <el-option
                v-for="item in customerOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="开票主体">
            <el-select
              v-model="conditions.filters.supplierCorporationId"
              placeholder="请选择开票主体"
              style="width: 280px"
              clearable
              filterable
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select
              v-model="conditions.filters.status"
              placeholder="请选择状态"
              style="width: 280px"
              clearable
            >
              <el-option label="待开票" value="PENDING"></el-option>
              <el-option label="已开票" value="INVOICED"></el-option>
              <el-option label="已退回" value="RETURNED"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="开票时间">
            <el-date-picker
              v-model="createTimeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              @change="handleCreateTimeChange"
              style="width: 280px"
            ></el-date-picker>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <!-- 操作按钮 -->
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="handleAdd">申请开票</el-button>
    </div>

    <!-- 数据列表 -->
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="发票申请ID"
        width="100"
      ></el-table-column>
      <el-table-column
        prop="customerName"
        label="所属客户"
        width="150"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="items[0].billMonth"
        label="账单月"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="title"
        label="发票抬头"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="typeDesc"
        label="发票类型"
        width="120"
      ></el-table-column>
      <el-table-column
        prop="supplierCorporationName"
        label="开票主体"
        width="200"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="fee"
        label="开票金额"
        width="150"
      ></el-table-column>
      <el-table-column
        prop="createTime"
        label="申请时间"
        width="180"
      ></el-table-column>
      <el-table-column
        prop="statusDesc"
        label="状态"
        width="120"
      ></el-table-column>
      <el-table-column fixed="right" label="操作" width="220">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleView(scope.row)"
            >查看</el-button
          >
          <el-button type="text" size="small" @click="handleDownload(scope.row)"
            >下载</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleConfirm(scope.row)"
            v-if="scope.row.status === 'PENDING'"
            >确认开票</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleReturn(scope.row)"
            v-if="scope.row.status === 'PENDING'"
            >退回</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    >
    </el-pagination>

    <UploadFile
      v-if="uploadDialogVisible"
      :visible="uploadDialogVisible"
      :invoice-id="selectedInvoiceId"
      @close="handleUploadDialogClose"
    />
    <ReturnDialog
      v-if="returnDialogVisible"
      :visible="returnDialogVisible"
      :invoice-id="selectedInvoiceId"
      @close="handleReturnDialogClose"
      @success="handleReturnSuccess"
    />
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import UploadFile from './invoices/uploadFile.vue'
import ReturnDialog from './invoices/return.vue'

const client = makeClient()

export default {
  name: 'Invoices',
  components: {
    UploadFile,
    ReturnDialog
  },
  data() {
    return {
      createTimeRange: [],
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        filters: {
          sn: '',
          customerId: '',
          supplierCorporationId: '',
          status: null,
          createTimeStart: '',
          createTimeEnd: ''
        }
      },
      customerOptions: [],
      supplierOptions: [],
      data: [],
      loading: true,
      total: 0,
      fullShown: false,
      uploadDialogVisible: false,
      returnDialogVisible: false,
      selectedInvoiceId: null
    }
  },
  async created() {
    await this.getList()
    await this.loadCustomerOptions()
    await this.loadSupplierOptions()
  },
  methods: {
    async getList() {
      this.loading = true
      try {
        const [err, r] = await client.supplierInvoicesList({
          body: this.conditions
        })
        if (err) {
          handleError(err)
          return
        }
        this.data = r.data.list || []
        this.total = r.data.total || 0
      } finally {
        this.loading = false
      }
    },
    // 加载客户选项
    async loadCustomerOptions() {
      const conditions = {
        filters: {
          supplierCorporationId: null,
        }
      }
      try {
        const [err, response] = await client.supplierListCustomer({
          body: { filters: conditions.filters }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.createTimeRange = []
      this.conditions.filters = {
        sn: '',
        customerId: '',
        supplierCorporationId: '',
        status: null,
        createTimeStart: '',
        createTimeEnd: ''
      }
      this.onSearch()
    },
    handleCreateTimeChange(value) {
      if (value && value.length === 2) {
        this.conditions.filters.createTimeStart = value[0]
        this.conditions.filters.createTimeEnd = value[1]
      } else {
        this.conditions.filters.createTimeStart = null
        this.conditions.filters.createTimeEnd = null
      }
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    handleAdd() {
      this.$router.push('/invoices/new')
    },
    handleView(row) {
      this.$router.push(`/invoices/${row.id}`)
    },
    handleDownload(row) {
      // TODO: Implement download invoice
      this.$message.info(`下载发票 ${row.id}`)
    },
    handleConfirm(row) {
      this.selectedInvoiceId = row.id
      this.uploadDialogVisible = true
    },
    handleUploadDialogClose() {
      this.uploadDialogVisible = false
      this.selectedInvoiceId = null
      this.getList()
    },
    handleReturn(row) {
      this.selectedInvoiceId = row.id
      this.returnDialogVisible = true
    },
    handleReturnDialogClose() {
      this.returnDialogVisible = false
      this.selectedInvoiceId = null
    },
    handleReturnSuccess() {
      this.handleReturnDialogClose()
      this.getList()
    }
  }
}
</script>

<style scoped></style>
