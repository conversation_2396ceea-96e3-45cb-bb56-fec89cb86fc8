<template>
  <div v-loading="loading">
    <el-form :model="form" :rules="rules" ref="form" label-width="120px" style="width: 1000px; margin-left: 50px;">

      <Title title="基本信息" />

      <!-- 第一行：合同名称 和 客户 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="合同名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入合同名称"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户" prop="customerId">
            <el-select filterable v-model="form.customerId" placeholder="请选择客户" style="width: 100%">
              <el-option v-for="customer in customerOptions" :key="customer.id" :label="customer.name"
                :value="customer.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第二行：作业主体 和 合同期限 -->
      <el-row :gutter="40">
        <el-col :span="12">
         <el-form-item label="合同有效期" prop="dateRange">
            <el-date-picker v-model="form.dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期" style="width: 100%" @change="handleDateRangeChange">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="作业主体" prop="supplierCorporationId">
            <el-select filterable v-model="form.supplierCorporationId" placeholder="请选择作业主体" style="width: 100%">
              <el-option v-for="corp in corporationOptions" :key="corp.id" :label="corp.name" :value="corp.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第三行：合同有效期 -->
      <el-row :gutter="40" v-if="!form.timeFixed">
        <el-col :span="12">
           <el-form-item  v-if="false" label="合同期限" prop="timeFixed">
            <el-radio-group v-model="form.timeFixed" @change="handleTimeFixedChange">
              <el-radio :label="true">固定期限</el-radio>
              <el-radio :label="false">无固定期限</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第四行：操作角色 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="操作角色" prop="roleIds">
            <RolesSelector v-if="shouldShowRolesSelector" v-model="form.roleIds" style="width: 100%" />
            <div v-else style="color: #999;">加载中...</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 第五行：合同附件 -->
      <el-row :gutter="40">
        <el-col :span="24">
          <el-form-item label="合同附件上传" prop="fileIds">
            <FileUploader v-if="shouldShowRolesSelector" v-model="form.fileIds" :multi="true" :max="10" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png" />
            <div v-else style="height: 32px; line-height: 32px; color: #999;">加载中...</div>
            <div style="margin-top: 8px; font-size: 12px; color: #999;">支持多个文件上传</div>
          </el-form-item>
        </el-col>
      </el-row>


      <Title title="发票信息" />

      <!-- 发票信息第一行 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="抬头" prop="invoiceTitle">
            <el-input v-model="form.invoiceTitle" placeholder="请输入发票抬头"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="纳税人识别号" prop="invoiceTaxNo">
            <el-input v-model="form.invoiceTaxNo" placeholder="请输入纳税人识别号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 发票信息第二行 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="开户行" prop="invoiceBankName">
            <el-input v-model="form.invoiceBankName" placeholder="请输入开户行"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="账号" prop="invoiceBankAccount">
            <el-input v-model="form.invoiceBankAccount" placeholder="请输入银行账号"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 发票信息第三行 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="注册地址" prop="invoiceRegisterAddress">
            <el-input v-model="form.invoiceRegisterAddress" placeholder="请输入注册地址"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="企业电话" prop="invoiceCompanyTel">
            <el-input v-model="form.invoiceCompanyTel" placeholder="请输入企业电话"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 发票备注 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="发票备注" prop="invoiceRemark">
            <el-input v-model="form.invoiceRemark" placeholder="请输入发票备注"></el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <Title title="业务配置" />

      <!-- 业务配置 -->
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="计算规则" prop="manageCalculationRule">
            <el-select v-model="form.manageCalculationRule" placeholder="请选择计算规则" style="width: 100%"
              @change="handleCalculationRuleChange">
              <el-option label="按雇员人数计算" value="EMPLOYEE_COUNT"></el-option>
              <el-option label="按应发金额比例计算" value="PAYABLE_AMOUNT_RATE"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <!-- 根据计算规则显示金额或费率 -->
          <el-form-item v-if="form.manageCalculationRule === 'EMPLOYEE_COUNT'" label="金额" prop="manageAmount">
            <el-input v-model="form.manageAmount" placeholder="请输入服务费金额">
              <template slot="append">元/人月</template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="form.manageCalculationRule === 'PAYABLE_AMOUNT_RATE'" label="费率" prop="manageRate">
            <el-input v-model="form.manageRate" placeholder="请输入费率分比">
              <template slot="append">%</template>
            </el-input>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 操作按钮 -->
      <el-form-item style="margin-top: 40px;">
        <el-button type="primary" @click="onSubmit" :loading="submitting">保存</el-button>
        <el-button @click="onCancel">取消</el-button>
      </el-form-item>

    </el-form>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import Title from './components/title.vue'
import FileUploader from './uploader/file.vue'
import RolesSelector from './selector/roles.vue'

const client = makeClient()

export default {
  components: { Title, FileUploader, RolesSelector },
  computed: {
    shouldShowRolesSelector() {
      return this.dataLoaded
    }
  },
  data() {
    return {
      loading: true,
      contractId: null,
      submitting: false,
      dataLoaded: false, // 数据加载完成标志
      // 客户选项
      customerOptions: [],

      // 作业主体选项
      corporationOptions: [],

      // 角色选项
      roleOptions: [],

      form: {
        dateRange: [],
        customerId: '',
        supplierCorporationId: '',
        name: '',
        timeFixed: true,
        startDate: '',
        endDate: '',
        remark: '',
        fileIds: [],
        invoiceTitle: '',
        invoiceTaxNo: '',
        invoiceBankName: '',
        invoiceBankAccount: '',
        invoiceRegisterAddress: '',
        invoiceCompanyTel: '',
        invoiceRemark: '',
        manageCalculationRule: '',
        manageAmount: '',
        manageRate: '',
        businessType: '',
        stopped: false,
        stopTime: '',
        stopReason: '',
        roleIds: []
      },
      rules: {
        name: [
          { required: true, message: '请输入合同名称', trigger: 'blur' }
        ],
        customerId: [
          { required: true, message: '请选择客户', trigger: 'change' }
        ],
        supplierCorporationId: [
          { required: true, message: '请选择作业主体', trigger: 'change' }
        ],
        timeFixed: [
          { required: true, message: '请选择合同期限类型', trigger: 'change' }
        ],
        dateRange: [
          { required: true, message: '请选择合同有效期', trigger: 'change' }
        ],
        roleIds: [
          { required: true, message: '请选择操作角色', trigger: 'change' }
        ],
        manageCalculationRule: [
          { required: true, message: '请选择计算规则', trigger: 'change' }
        ],
        manageAmount: [
          { required: true, message: '请输入服务费金额', trigger: 'blur' }
        ],
        manageRate: [
          { required: true, message: '请输入费率', trigger: 'blur' }
        ]
      }
    }
  },
  async created() {
    // 获取路由参数中的合同ID
    this.contractId = this.$route.params.id

    // 加载数据
    await this.loadCustomerOptions()
    await this.loadCorporationOptions()
    await this.loadContractData()
  },

  methods: {
    // 加载客户选项
    async loadCustomerOptions() {
      const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
        const [err, response] = await client.supplierListCustomer({
          body: { filters: conditions.filters }
        })

        if (response.success && response.data) {
          this.customerOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载客户选项失败：', error)
      }
    },

    // 加载作业主体选项
    async loadCorporationOptions() {
      const conditions = {
        filters: {
          corporationIds: [],
        }
      }
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: conditions.filters }
        })

        if (response.success && response.data) {
          this.corporationOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },

    // 加载合同数据
    async loadContractData() {
      try {
        const [err, response] = await client.queryContract({
          body: { id: this.contractId }
        })

        if (err) {
          this.$message.error(err.message)
          this.loading = false
          this.dataLoaded = true
          return
        }

        if (response.success) {
          const data = response.data || {}

          // 处理fileIds
          let processedFileIds = []
          if (data.fileIds) {
            if (typeof data.fileIds === 'string') {
              processedFileIds = data.fileIds.split(',').filter(id => id.trim())
            } else {
              processedFileIds = data.fileIds
            }
          }

          // 将API返回的数据映射到表单
          this.form = {
            customerId: data.customerId || '',
            supplierCorporationId: data.supplierCorporationId || '',
            name: data.name || '',
            timeFixed: data.timeFixed !== undefined ? data.timeFixed : true,
            startDate: data.startDate || '',
            endDate: data.endDate || '',
            remark: data.remark || '',
            fileIds: processedFileIds,
            invoiceTitle: data.invoiceTitle || '',
            invoiceTaxNo: data.invoiceTaxNo || '',
            invoiceBankName: data.invoiceBankName || '',
            invoiceBankAccount: data.invoiceBankAccount || '',
            invoiceRegisterAddress: data.invoiceRegisterAddress || '',
            invoiceCompanyTel: data.invoiceCompanyTel || '',
            invoiceRemark: data.invoiceRemark || '',
            manageCalculationRule: data.manageCalculationRule || '',
            manageAmount: data.manageAmount || '',
            manageRate: data.manageRate || '',
            businessType: data.businessType || '',
            stopped: data.stopped || false,
            stopTime: data.stopTime || '',
            stopReason: data.stopReason || '',
            roleIds: data.roleIds || [],
            dateRange: []
          }

          // 设置日期范围
          if (data.startDate && data.endDate) {
            this.form.dateRange = [new Date(data.startDate), new Date(data.endDate)]
          }
        } else {
          this.$message.error(response.message || '获取合同信息失败')
        }
        this.loading = false
        this.dataLoaded = true 
        console.log('this.formthis.formthis.form',this.form)
      } catch (error) {
        handleError(error)
        this.loading = false
        this.dataLoaded = true 
      }
    },

    handleTimeFixedChange(value) {
      if (value) {
        // 固定期限，清空日期范围
        this.form.dateRange = []
        this.form.startDate = ''
        this.form.endDate = ''
        this.$nextTick(() => {
          this.$refs.form.clearValidate(['dateRange'])
        })
      }
    },

    handleDateRangeChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.form.startDate = this.formatDate(dateRange[0])
        this.form.endDate = this.formatDate(dateRange[1])
      } else {
        this.form.startDate = ''
        this.form.endDate = ''
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate(['dateRange'])
      })
    },

    handleCalculationRuleChange(value) {
      if (value === 'EMPLOYEE_COUNT') {
        this.form.manageRate = ''
        this.$nextTick(() => {
          this.$refs.form.clearValidate(['manageRate'])
        })
      } else if (value === 'PAYABLE_AMOUNT_RATE') {
        this.form.manageAmount = ''
        this.$nextTick(() => {
          this.$refs.form.clearValidate(['manageAmount'])
        })
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return ''
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    // 提交表单
    async onSubmit() {
      const valid = await this.$refs.form.validate()
      if (!valid) {
        return
      }
      // 手动校验计算规则
      if (this.form.manageCalculationRule === 'EMPLOYEE_COUNT' && !this.form.manageAmount) {
        this.$message.error('请输入服务费金额')
        return
      }
      if (this.form.manageCalculationRule === 'PAYABLE_AMOUNT_RATE' && !this.form.manageRate) {
        this.$message.error('请输入费率')
        return
      }

      const submitData = { ...this.form }
      if (Array.isArray(submitData.fileIds)) {
        submitData.fileIds = submitData.fileIds.join(',')  // 转换为逗号分隔的字符串
      }

      try {
        this.submitting = true
        // 调用更新接口
        const [err, response] = await client.updateContract({
          body: {
            id: this.contractId,
            ...submitData
          }
        })

        if (response.success) {
          this.$message.success('合同更新成功')
          // 返回列表页面
          this.$router.push('/serviceContracts')
        } else {
          this.$message.error(response.message || '更新失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.submitting = false
      }
    },

    // 取消
    onCancel() {
      this.$router.back()
    }
  }
}
</script>

<style scoped></style>
