<template>
  <div
    class="roles"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 20px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div class="lite" style="display: flex; align-items: center">
        <el-form-item label="角色名称">
          <el-input
            v-model="conditions.filters.name"
            placeholder="请输入角色名称"
            style="width: 280px"
          ></el-input>
        </el-form-item>
        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
    </el-form>
    <div style="text-align: right; flex: 0 0 auto; padding: 10px 0px">
      <el-button type="primary" @click="$router.push('/roles/new')">
        <i class="el-icon-plus" />
        新建角色
      </el-button>
    </div>
    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="name"
        label="角色名称"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="remark"
        label="角色描述"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="授权数量">
        <template slot-scope="scope">
          {{ scope.row.members ? scope.row.members.length : 0 }}
        </template>
      </el-table-column>
      <el-table-column prop="modifyTime" label="更新时间"></el-table-column>
      <el-table-column prop="createTime" label="创建时间"></el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <el-switch
            :value="!scope.row.disabled"
            @change="handleDisable(scope.row)"
            active-color="#13ce66"
            inactive-color="#ff4949"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="190">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="goUsers(scope.row.id)"
            >已授权用户</el-button
          >
          <el-button type="text" size="small" @click="handleEdit(scope.row.id)"
            >编辑</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="handleDelete(scope.row.id)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: ''
        }
      },
      total: 0,
      data: [],
      loading: true
    }
  },
  async created() {
    await this.getList()
  },
  methods: {
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions = {
        offset: 0,
        limit: 10,
        sorts: [
          // {
          //   field: '',
          //   direction: ''
          // }
        ],
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          name: ''
        }
      }

      this.getList()
    },
    async getList() {
      this.loading = true

      const [err, r] = await client.listRoles({
        body: this.conditions
      })

      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      console.log('page===', page)
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    async handleDisable(row) {
      const action = row.disabled ? '启用' : '禁用'
      try {
        await this.$confirm(`确定要${action}该角色吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        return
      }

      const [err] = await client.disableRole({
        body: {
          id: row.id,
          disabled: !row.disabled
        }
      })

      if (err) {
        handleError(err)
        return
      }

      this.$message.success(`${action}成功`)
      this.getList()
    },
    goUsers(id) {
      this.$router.push(`/supplierUsers?roleId=${id}`)
    },
    handleEdit(id) {
      this.$router.push(`/roles/${id}/edit`)
    },
    async handleDelete(id) {
      try {
        await this.$confirm('确定要删除该角色吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
      } catch (error) {
        return
      }

      const [err] = await client.deleteRole({ body: { roleId: id } })

      if (err) {
        handleError(err)
        return
      }

      this.$message.success('删除成功')
      if (this.conditions.offset > 0 && this.data.length === 1) {
        this.conditions.offset -= this.conditions.limit
        this.getList()
      } else {
        this.getList()
      }
    }
  }
}
</script>
