<template>
  <div class="supplierLaborsSelector">
    <el-select
      v-model="selectedLaborIds"
      :multiple="multiple"
      filterable
      clearable
      remote
      :remote-method="remoteMethod"
      placeholder="请输入人员名称搜索"
      style="width: 100%"
      :loading="loading"
      value-key="id"
      @change="handleChange"
    > 
     <el-option
        v-if="multiple && labors.length > 0"
        key="selectAll"
        label="全选"
        value="selectAll"
        @click.native="handleSelectAll"
      >
        <el-checkbox 
          :value="isAllSelected" 
          @change="handleSelectAll"
          @click.native.stop
        >
          全选
        </el-checkbox>
      </el-option>
      <el-option
        v-for="item in labors"
        :key="item.id"
        :label="item.name"
        :value="item.id"
      >
        <span style="float: left">{{ item.name }}</span>
        <span style="float: right; color: #8492a6; font-size: 13px">{{
          item.shortName
        }}</span>
      </el-option>
    </el-select>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'SupplierLaborsSelector',
  props: {
    multiple: {
      type: Boolean,
      default: true
    },
    value: {
      type: [Array, String, Number],
      default() {
        return this.multiple ? [] : null
      }
    },
    contractId: {
      type: Number,
      default: null
    }
  },
  data() {
    return {
      loading: false,
      labors: [], // Labors from search results
      selectedLaborIds: this.multiple ? [] : null
    }
  },
  watch: {
    value: {
      handler(newValue) {
        if (this.multiple) {
          const laborIds = (newValue || []).map(id => Number(id))
          this.selectedLaborIds = laborIds
          if (laborIds.length > 0) {
            this.fetchLaborsByIds(laborIds)
          }
        } else {
          const laborId = newValue ? Number(newValue) : null
          this.selectedLaborIds = laborId
          if (laborId) {
            this.fetchLaborsByIds([laborId])
          }
        }
      },
      immediate: true
    },
    contractId(v) {
      if(v) {
        this.fetchLabors()
      }else {
        this.selectedLaborIds = this.multiple? [] : null
        this.$emit('input', this.selectedLaborIds)
        this.labors = []
      }
    }
  },
  computed: {
    isAllSelected() {
      if (!this.multiple || this.labors.length === 0) return false
      return this.labors.length > 0 && this.labors.every(labor => 
        this.selectedLaborIds.includes(labor.id)
      )
    }
  },
  methods: {
    handleSelectAll() {
      if (!this.multiple) return
      
      if (this.isAllSelected) {
        // 取消全选
        this.selectedLaborIds = []
      } else {
        // 全选
        this.selectedLaborIds = this.labors.map(labor => labor.id)
      }
      this.handleChange(this.selectedLaborIds)
    },
    reset() {
      this.selectedLaborIds = this.multiple ? [] : null
    },
    async fetchLabors(name = '') {
      this.loading = true
      const [err, r] = await client.supplierLaborList({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            name: name,
            contractId: this.contractId
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.labors = r.data?.list || []
    },
    async fetchLaborsByIds(laborIds) {
      if (!laborIds || laborIds.length === 0) return

      const missingIds = laborIds.filter(
        id => !this.labors.find(labor => labor.id === id)
      )

      if (missingIds.length === 0) return

      const [err, r] = await client.supplierLaborList({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: true,
          withDeleted: true,
          filters: {
            ids: missingIds
          }
        }
      })

      if (err) {
        handleError(err)
        return
      }

    },
    remoteMethod(query) {
      this.fetchLabors(query)
    },
    handleChange(value) {
      this.$emit('input', value)
    }
  }
}
</script>

<style scoped>
.supplierLaborsSelector {
  width: 100%;
}
</style>
