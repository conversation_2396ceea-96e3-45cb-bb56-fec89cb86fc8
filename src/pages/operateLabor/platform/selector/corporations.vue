<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    :multiple="multiple"
    :clearable="clearable"
    :filterable="filterable"
    :loading="loading"
    :remote="remote"
    :remote-method="handleRemoteSearch"
    @change="handleChange"
  >
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    >
      <span style="float: left">{{ item.name }}</span>
      <span style="float: right; color: #8492a6; font-size: 13px">
        {{ item.socialCreditCode }}
      </span>
    </el-option>
  </el-select>
</template>

<script>
import makeClient from '../../../../services/operateLabor/makeClient'
import handleError from '../../../../helpers/handleError'

const client = makeClient()

export default {
  name: 'CorporationsSelector',

  props: {
    value: {
      type: [Number, Array],
      default: () => []
    },
    templateId: {
      type: Number,
      default: null
    },
    corporationIdOptions: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择作业主体'
    },
    multiple: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    },
    filterable: {
      type: Boolean,
      default: true
    },
    remote: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      selectedValue: this.multiple ? [] : null,
      options: [],
      loading: false,
    }
  },

  watch: {
    templateId: {
      handler(newVal) {
        console.log('templateId changed:', newVal)
        this.options = []
        this.loadCorporations()
      },
      immediate: true
    }
  },


  methods: {
    async loadCorporations(keyword = '') {
      this.loading = true

      try {
        const [err, response] = await client.listCorporation({
          body: {
            offset: 0,
            limit: 100,
            withTotal: false,
            withDisabled: true, // 包含已禁用的，以防编辑时选中的是已禁用的
            withDeleted: false,
            filters: {
              name: keyword
            }
          }
        })

        if (err) {
          handleError(err)
          return
        }
        this.selectedValue = this.value
        // if (!this.multiple) {
        //   this.options = response.data.list.filter(
        //     i => i.id === this.selectedValue
        //   )
        // } else {
          this.options = response.data.list.filter(item =>
            this.corporationIdOptions.includes(item.id)
          )
        // }
        // const newOptions = response.data?.list || []

        // // 合并现有选项和新选项，避免重复
        // const existingIds = this.options.map(opt => opt.id)
        // const uniqueNewOptions = newOptions.filter(
        //   opt => !existingIds.includes(opt.id)
        // )

        // if (keyword) {
        //   // 搜索时替换选项
        //   this.options = newOptions
        // } else {
        //   // 初始加载时合并选项
        //   this.options = [...this.options, ...uniqueNewOptions]
        // }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },

    handleChange(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    },

    handleRemoteSearch(keyword) {
      if (keyword) {
        this.loadCorporations(keyword)
      } else {
        this.loadCorporations()
      }
    }
  }
}
</script>

<style scoped>
::v-deep .el-select {
  width: 100%;
}

::v-deep .el-select-dropdown__item {
  height: auto;
  line-height: 1.4;
  padding: 8px 20px;
}

::v-deep .el-select-dropdown__item span {
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
