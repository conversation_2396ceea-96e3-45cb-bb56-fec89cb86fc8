<template>
  <div class="serviceContractsSelector">
    <el-select
      v-model="selectedContractId"
      clearable
      :placeholder="placeholder"
      style="width: 100%"
      :loading="loading"
      value-key="id"
      :disabled="disabled"
      @change="handleChange"
    >
      <el-option
        v-for="item in contracts"
        :key="item.id"
        :label="item.name + ' | ' + item.customerName"
        :value="item.id"
      >
      </el-option>
    </el-select>
  </div>
</template>

<script>
import handleError from '../../../../helpers/handleError'
import makeClient from '../../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'ServiceContractsSelector',
  props: {
    value: {
      type: [String, Number],
      default: null
    },
    customerId: {
      type: [String, Number],
      default: null
    },
    corporationId: {
      type: [String, Number],
      default: null
    },
    placeholder: {
      type: String,
      default: '请选择服务合同'
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    }
  },
  watch: {
    corporationId: {
      handler(v) {
        this.contracts = []
        this.selectedContractId = null
        this.$emit('input', null)
        this.fetchContracts()
      },
      deep: true
    },
    customerId: {
      handler(v) {
        this.contracts = []
        this.selectedContractId = null
        this.$emit('input', null)
        this.fetchContracts()
      },
      deep: true
    }
  },
  data() {
    return {
      loading: false,
      contracts: [],
      selectedContractId: null
    }
  },
  methods: {
    reset() {
      this.selectedContractId = null
    },
    remoteMethod(query) {
      this.fetchContracts(query)
    },
    async fetchContracts() {
      if (!this.customerId || !this.corporationId) return
      this.loading = true
      const [err, r] = await client.listContractByCustomerAndCorporation({
        body: {
          offset: 0,
          limit: 1000,
          withTotal: false,
          withDisabled: false,
          withDeleted: false,
          filters: {
            customerId: this.customerId,
            supplierCorporationId: this.corporationId
          }
        }
      })
      this.loading = false

      if (err) {
        handleError(err)
        return
      }

      this.contracts = r.data.list || []
    },
    handleChange(value) {
      this.$emit('input', value)
      this.$emit('change', value)
    }
  }
}
</script>

<style scoped>
.serviceContractsSelector {
  width: 100%;
}
</style>
