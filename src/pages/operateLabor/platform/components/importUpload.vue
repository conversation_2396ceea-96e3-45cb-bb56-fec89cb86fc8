<template>
  <div>
    <el-upload
      drag
      class="upload-demo"
      action=""
      :http-request="uploadFile"
      :before-upload="beforeUpload"
      :on-change="handleChange"
      :on-remove="handleMove"
      :file-list="fileList"
      :disabled="disabled"
    >
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">拖拽文件到这里上传</div>
      <div class="el-upload__text">
        <el-button type="primary" size="mini" style="margin-top: 10px">{{
          isUploadFile ? '重新上传' : '选择文件'
        }}</el-button>
        <div style="padding-top: 10px">
          仅支持格式为xls、xlsx的文件，且文件大小不超过5Mb
        </div>
        <div class="upload-template">
          如有需要也可使用系统模板
          <span
            style="color: #4f71ff; margin-left: 5px; cursor: pointer"
            @click="downloadTemplate"
          >
            <i class="el-icon-download"></i>下载模板
          </span>
        </div>
      </div>
    </el-upload>
  </div>
</template>
<script>
import { exportExcel } from 'kit/helpers/exportExcel'
import handleError from 'kit/helpers/handleError'
import makeClient from 'kit/services/operateLabor/makeClient'
const client = makeClient()

export default {
  props: {
    value: {
      type: Array,
      default: () => []
    },
    taxCalculationMethod: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileList: [],
      isUploadFile: false,
      disabled: false
    }
  },
  methods: {
    async uploadFile(params) {
      this.$emit('getFileParams', params)
    },
    beforeUpload(file) {
      var testmsg = file.name.substring(file.name.lastIndexOf('.') + 1)
      const isXls = testmsg === 'xls' || testmsg === 'xlsx'
      const isLt5M = file.size / 1024 / 1024 < 5
      if (!isXls) {
        this.$message.warning('上传文件类型只能是 xls,xlsx 格式!')
        this.fileList = []
        return false
      }
      if (!isLt5M) {
        this.$message.warning('上传文件大小不能超过 5MB!')
        this.fileList = []
        return false
      }
    },
    handleMove() {
      this.fileList = []
      this.isUploadFile = false
      this.$emit('input', [])
    },
    handleChange(file, fileList) {
      this.fileList = [file]
      this.isUploadFile = true
      this.$emit('input', fileList)
    },
    //模板下载
    async downloadTemplate() {
      this.disabled = true
      const result = await client.supplierSalaryDownloadTemplate(
        this.taxCalculationMethod
      )
      this.disabled = false
      await exportExcel(result)
    },
  }
}
</script>
<style scoped>
.upload-demo {
  width: 500px;
  margin: 0 auto;
  position: relative;
}
.upload-template {
  position: absolute;
  bottom: 10px;
  left: 80px;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
}
::v-deep .el-upload-dragger {
  width: 500px;
  height: 230px;
  border: 1px dashed #d9d9d9;
}
</style>
