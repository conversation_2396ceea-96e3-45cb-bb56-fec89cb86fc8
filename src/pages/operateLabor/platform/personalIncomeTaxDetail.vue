<template>
  <div v-loading="loading">
    <!-- 申报表基本信息 -->
    <div style="background: white; padding: 20px; margin-bottom: 20px">
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 20px;
        "
      >
        <h1
          style="
            font-size: 25px;
            font-weight: 600;
            color: #262626;
            margin: 0;
            line-height: 1.2;
          "
        >
          {{ taxData.supplierCorporationName }}
        </h1>
        <el-button type="primary" @click="handleExport">导出申报表</el-button>
      </div>

      <!-- 基本信息展示 -->
      <div
        style="
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 20px;
          font-size: 14px;
        "
      >
        <div>
          <span style="color: #8c8c8c">税款所属期：</span>
          <span>{{ formatText(taxData.taxPaymentPeriod) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c">个税申报月：</span>
          <span>{{ formatText(taxData.incomeTaxMonth) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c">总人数：</span>
          <span>{{ formatText(taxData.taxpayersCount) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c">本期收入总额：</span>
          <span>{{ formatAmount(taxData.currentIncome) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c">总实发金额：</span>
          <span>{{ formatAmount(taxData.totalActualAmount) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c">总预扣预缴税额：</span>
          <span>{{ formatAmount(taxData.currentWithholdingTax) }}</span>
        </div>
        <!-- <div>
          <span style="color: #8c8c8c;">个税申报表ID：</span>
          <span>{{ formatText(taxData.id) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">作业主体ID：</span>
          <span>{{ formatText(taxData.supplierCorporationId) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">作业主体名称：</span>
          <span>{{ formatText(taxData.supplierCorporationName) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">创建时间：</span>
          <span>{{ formatText(taxData.createTime) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">修改时间：</span>
          <span>{{ formatText(taxData.modifyTime) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">灵工主体ID：</span>
          <span>{{ formatText(taxData.supplierId) }}</span>
        </div>
        <div>
          <span style="color: #8c8c8c;">生成状态：</span>
          <span :class="['status-tag', getStatusClass(taxData.status)]">
            {{ getStatusText(taxData.status) }}
          </span>
        </div> -->
      </div>
    </div>

    <!-- 个税申报详情列表 -->
    <el-tabs v-model="activeTab" type="card" style="padding: 20px">
      <el-tab-pane label="申报详情" name="detail">
        <div style="padding: 5px">
          <el-table
            v-loading="detailLoading"
            :data="detailData"
            style="width: 100%; margin-top: 10px"
            :header-cell-style="{
              'font-size': '12px',
              'font-weight': '400',
              color: '#777c94',
              background: 'var(--o-primary-bg-color)'
            }"
          >
            <el-table-column
              prop="name"
              label="姓名"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatText(scope.row.name) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="idCard"
              label="身份证号"
              width="180"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatText(scope.row.idCard) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="cellphone"
              label="手机号"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatText(scope.row.cellphone) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="currentIncome"
              label="本期收入"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.currentIncome) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="actualAmount"
              label="实发金额"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.actualAmount) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedIncome"
              label="累计收入"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedIncome) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedExpenses"
              label="累计费用"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedExpenses) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedTaxFreeIncome"
              label="累计免税收入"
              width="140"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedTaxFreeIncome) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedTaxDeductionExpenses"
              label="累计减除费用"
              width="140"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedTaxDeductionExpenses) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedOtherDeductions"
              label="累计依法确定的其他扣除"
              width="190"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedOtherDeductions) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedPrepaidTax"
              label="累计已预缴税额"
              width="140"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedPrepaidTax) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedTaxReductions"
              label="累计减免税额"
              width="140"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedTaxReductions) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="accumulatedTaxableAmount"
              label="累计应纳税额"
              width="140"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.accumulatedTaxableAmount) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="currentWithholdingTax"
              label="本期应预扣预缴税额"
              width="150"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatAmount(scope.row.currentWithholdingTax) }}
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="taxPeriod"
              label="税款所属期"
              width="120"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatText(scope.row.taxPeriod) }}
              </template>
            </el-table-column>
            <el-table-column
              prop="declareMonth"
              label="申报月份"
              width="100"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                {{ formatText(scope.row.declareMonth) }}
              </template>
            </el-table-column> -->
          </el-table>

          <el-pagination
            v-if="detailTotal > 0"
            @current-change="handleDetailCurrentChange"
            :current-page="detailConditions.offset / detailConditions.limit + 1"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="detailConditions.limit"
            layout="total, prev, pager, next"
            :total="detailTotal"
            style="text-align: right; margin-top: 10px"
          ></el-pagination>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import { getToken } from '../../../helpers/token'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  data() {
    return {
      activeTab: 'detail',
      loading: true,
      detailLoading: false,
      taxData: {
        id: '',
        supplierCorporationId: '',
        taxPaymentPeriod: '',
        incomeTaxMonth: '',
        taxpayersCount: '',
        currentIncome: '',
        supplierCorporationName: '',
        createTime: '',
        modifyTime: '',
        supplierId: '',
        currentWithholdingTax: '',
        totalActualAmount: '',
        status: ''
      },
      detailData: [],
      allDetailData: [], // 存储所有详情数据
      detailTotal: 0,
      detailConditions: {
        offset: 0,
        limit: 10
      }
    }
  },
  async created() {
    const id = this.$route.params.id
    if (id) {
      await this.loadTaxDetail(id)
    }
  },
  methods: {
    async loadTaxDetail(id) {
      this.loading = true
      try {
        const [err, response] = await client.queryPersonalTax({
          body: { id: parseInt(id) }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response.success && response.data) {
          this.taxData = response.data
          this.allDetailData = response.data.details || []
          this.detailTotal = this.allDetailData.length
          this.updateDetailData()
        } else {
          this.$message.error(response.message || '加载数据失败')
        }
      } catch (error) {
        handleError(error)
      } finally {
        this.loading = false
      }
    },
    handleDetailCurrentChange(page) {
      this.detailConditions.offset = (page - 1) * this.detailConditions.limit
      this.updateDetailData()
    },
    updateDetailData() {
      const start = this.detailConditions.offset
      const end = start + this.detailConditions.limit
      this.detailData = this.allDetailData.slice(start, end)
    },
    async handleExport() {
      try {
        // 使用fetch API携带token下载文件
        const token = `Bearer ${getToken()}`
        const response = await fetch(
          `${window.env?.apiPath}/api/supplier/personaltax/download/declarationRecord`,
          {
            method: 'POST',
            headers: {
              Authorization: token,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              id: this.taxData.id
            })
          }
        )

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        // 获取文件blob
        const blob = await response.blob()

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `${this.taxData.taxPaymentPeriod}_个税申报表.xlsx`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        // 清理URL对象
        window.URL.revokeObjectURL(url)

        this.$message.success('申报表导出成功')
      } catch (error) {
        console.error('导出申报表失败：', error)
        this.$message.error('导出申报表失败')
      }
    },
    getStatusText(status) {
      const statusMap = {
        GENERATING: '生成中',
        GENERATED: '已生成'
      }
      return statusMap[status] || status
    },
    getStatusClass(status) {
      const classMap = {
        GENERATING: 'status-generating',
        GENERATED: 'status-generated'
      }
      return classMap[status] || 'status-default'
    },
    formatAmount(amount) {
      if (amount === null || amount === undefined || amount === '') {
        return '-'
      }
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    formatText(text) {
      if (text === null || text === undefined || text === '') {
        return '-'
      }
      return text
    }
  }
}
</script>

<style scoped>
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-generating {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-generated {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>
