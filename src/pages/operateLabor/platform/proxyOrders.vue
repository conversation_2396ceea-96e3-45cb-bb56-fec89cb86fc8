<template>
  <div
    class="proxy-orders"
    style="display: flex; flex-direction: column; height: 100%"
  >
    <el-form
      :inline="true"
      class="search"
      style="
        flex: 0 1 auto;
        margin-bottom: 10px;
        background: var(--o-primary-bg-color);
        padding: 20px 20px 0 20px;
        border-radius: 5px;
      "
      label-position="right"
      label-width="90px"
    >
      <div
        class="lite"
        v-if="!fullShown"
        style="display: flex; align-items: center"
      >
        <div>
          <!-- <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.corporationIds"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
              multiple
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="代付批次ID">
            <el-input
              v-model="conditions.filters.batchId"
              placeholder="请输入批次ID"
              style="width: 280px"
              @input="validateBatchId"
            ></el-input>
          </el-form-item>
           <el-form-item label="工资表ID">
            <el-input
              v-model="conditions.filters.salaryDetailId"
              placeholder="请输入工资表ID"
              style="width: 280px"
              @input="validateSalaryDetailId"
            ></el-input>
          </el-form-item>
          <el-button
            type="text"
            @click="fullShown = true"
            style="position: relative; top: 5px"
            >展开</el-button
          >
        </div>

        <div style="text-align: right; flex: 1; position: relative; top: -11px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </div>
      </div>
      <div class="full" v-else>
        <div>
          <!-- <el-form-item label="作业主体">
            <el-select
              filterable
              v-model="conditions.filters.corporationIds"
              placeholder="请选择所属作业主体"
              style="width: 280px"
              clearable
              multiple
            >
              <el-option
                v-for="item in supplierOptions"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item> -->
          <el-form-item label="代付批次ID">
            <el-input
              v-model="conditions.filters.batchId"
              placeholder="请输入批次ID"
              style="width: 280px"
              @input="validateBatchId"
            ></el-input>
          </el-form-item>
          <el-form-item label="工资表ID">
            <el-input
              v-model="conditions.filters.salaryDetailId"
              placeholder="请输入工资表ID"
              style="width: 280px"
              @input="validateSalaryDetailId"
            ></el-input>
          </el-form-item>
        </div>
        <div>
          <el-form-item label="状态">
            <el-select
              v-model="conditions.filters.status"
              placeholder="请选择状态"
              style="width: 280px"
              clearable
            >
              <el-option label="创建" value="CREATE"></el-option>
              <el-option label="校验成功" value="CHECK_SUCC"></el-option>
              <el-option label="校验失败" value="CHECK_FAIL"></el-option>
              <el-option label="处理中" value="PROCESSING"></el-option>
              <el-option label="已汇款" value="REMIT"></el-option>
              <el-option label="失败" value="FAIL"></el-option>
              <el-option label="已退款" value="REFUND"></el-option>
              <el-option label="已删除" value="DELETED"></el-option>
            </el-select>
          </el-form-item>
          <el-button
            type="text"
            style="position: relative; top: 5px"
            @click="fullShown = false"
            >收起</el-button
          >
        </div>
        <el-form-item label=" ">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button type="default" @click="onReset">重置</el-button>
        </el-form-item>
      </div>
    </el-form>

    <el-table
      v-loading="loading"
      size="small"
      :data="data"
      style="flex: 1 1 auto"
      height="100%"
      :header-cell-style="{
        'font-size': '12px',
        'font-weight': '400',
        color: '#777c94',
        background: 'var(--o-primary-bg-color)'
      }"
    >
      <el-table-column
        prop="id"
        label="代发订单ID"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="proxyBatchId"
        label="代付批次ID"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="salaryDetailId"
        label="工资表ID"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="name"
        label="姓名"
        width="100"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="idCard"
        label="身份证号"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="cellphone"
        label="手机号"
        width="120"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column
        prop="currentTaxWithholding"
        label="本次预扣预缴个税"
        width="150"
      >
        <template slot-scope="scope">{{
          formatCurrency(scope.row.currentTaxWithholding)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="vatAmount"
        label="增值税"
        width="120"
      >
        <template slot-scope="scope">{{
          formatCurrency(scope.row.vatAmount)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="additionalTaxAmount"
        label="增值附加税"
        width="120"
      >
        <template slot-scope="scope">{{
          formatCurrency(scope.row.additionalTaxAmount)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="taxReliefAmount"
        label="补缴增值税"
        width="120"
      >
        <template slot-scope="scope">{{
          formatCurrency(scope.row.taxReliefAmount)
        }}</template>
      </el-table-column>
      <el-table-column
        prop="amount"
        label="实发金额"
        width="120"
      >
        <template slot-scope="scope">{{
          formatCurrency(scope.row.amount)
        }}</template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <span :class="['status-tag', getStatusClass(scope.row.status)]">
            {{ getStatusText(scope.row.status) }}
          </span>
        </template>
      </el-table-column>
      <el-table-column
        prop="lastErrorInfo"
        label="失败原因"
        width="180"
        show-overflow-tooltip
      ></el-table-column>
    </el-table>

    <el-pagination
      @current-change="handleCurrentChange"
      :current-page="conditions.offset / conditions.limit + 1"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="conditions.limit"
      layout="total, prev, pager, next"
      :total="total"
      style="flex: 0 0 auto; text-align: right; margin-top: 10px"
    ></el-pagination>
  </div>
</template>

<script>
import handleError from '../../../helpers/handleError'
import makeClient from '../../../services/operateLabor/makeClient'
import ServiceContractsSelector from './selector/serviceContracts.vue'
const client = makeClient()

export default {
  components: {
    ServiceContractsSelector
  },
  data() {
    return {
      fullShown: false,
      conditions: {
        offset: 0,
        limit: 10,
        withTotal: true,
        withDisabled: true,
        withDeleted: true,
        filters: {
          corporationIds: [],
          batchId: '',
          salaryDetailId: '',
          contractIds: [],
          status: null
        }
      },
      total: 0,
      data: [],
      loading: true,
      supplierOptions: []
    }
  },
  async created() {
    await this.loadSupplierOptions()
    await this.getList()
  },
  methods: {
    // 验证代付批次ID只能输入数字
    validateBatchId() {
      if (this.conditions.filters.batchId) {
        this.conditions.filters.batchId = this.conditions.filters.batchId.replace(/[^\d]/g, '')
      }
    },
    // 验证工资表ID只能输入数字
    validateSalaryDetailId() {
      if (this.conditions.filters.salaryDetailId) {
        this.conditions.filters.salaryDetailId = this.conditions.filters.salaryDetailId.replace(/[^\d]/g, '')
      }
    },
    // 加载作业主体选项
    async loadSupplierOptions() {
      try {
        const [err, response] = await client.listCorporation({
          body: { filters: {} }
        })

        if (err) {
          handleError(err)
          return
        }

        if (response && response.success && response.data) {
          this.supplierOptions = response.data.list || []
        }
      } catch (error) {
        console.error('加载作业主体选项失败：', error)
      }
    },
    onSearch() {
      this.conditions.offset = 0
      this.getList()
    },
    onReset() {
      this.conditions.filters = {
        corporationIds: [],
        batchId: '',
        salaryDetailId: '',
        contractIds: [],
        status: null
      }
      this.getList()
    },
    async getList() {
      this.loading = true
      const [err, r] = await client.supplierListProxy({
        body: this.conditions
      })
      this.loading = false
      if (err) {
        handleError(err)
        return
      }
      this.data = r.data.list || []
      this.total = r.data.total || 0
    },
    handleCurrentChange(page) {
      this.conditions.offset = (page - 1) * this.conditions.limit
      this.getList()
    },
    formatCurrency(value) {
      if (value == null) return '0.00'
      return value.toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    },
    getStatusText(status) {
      const statusMap = {
        CREATE: '创建',
        CHECK_SUCC: '校验成功',
        CHECK_FAIL: '校验失败',
        PROCESSING: '处理中',
        REMIT: '出款成功',
        FAIL: '失败',
        REFUND: '退款',
        DELETED: '删除'
      }
      return statusMap[status] || status
    },
    getStatusClass(status) {
      const classMap = {
        CREATE: 'status-create',
        CHECK_SUCC: 'status-check-succ',
        CHECK_FAIL: 'status-check-fail',
        PROCESSING: 'status-processing',
        REMIT: 'status-remit',
        FAIL: 'status-fail',
        REFUND: 'status-refund',
        DELETED: 'status-deleted'
      }
      return classMap[status] || 'status-default'
    }
  }
}
</script>

<style scoped>
.status-tag {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: inline-block;
}

.status-create {
  background-color: #f0f9ff;
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.status-check-succ {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-check-fail {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-processing {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-remit {
  background-color: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.status-fail {
  background-color: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffccc7;
}

.status-refund {
  background-color: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.status-deleted {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}

.status-default {
  background-color: #fafafa;
  color: #666666;
  border: 1px solid #d9d9d9;
}
</style>