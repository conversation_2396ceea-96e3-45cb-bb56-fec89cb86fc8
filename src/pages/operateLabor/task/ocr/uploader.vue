<template>
  <div class="uploader" style="text-align: center">
    <div style="display: inline-block" @click="triggerUploader" v-if="!file">
      <slot name="uploadButton">
        <a style="cursor: pointer">上传文件</a>
      </slot>
    </div>
    <div v-else>
      <slot name="preview" :file="file" :remove="remove">
        <div style="display: flex; align-items: center">
          <span>{{ file?.name }}</span>
          <a style="cursor: pointer" @click="remove">删除</a>
        </div>
      </slot>
    </div>
    <input
      type="file"
      accept="image/*"
      ref="realUploader"
      @change="handleFileChange"
      style="display: none"
    />
  </div>
</template>

<script>
import handleErrorH5 from 'kit/helpers/handleErrorH5'
import makeClient from 'kit/services/operateLabor/makeClient'

const client = makeClient()
export default {
  props: {
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      file: null,
      uploading: false
    }
  },
  async created() {
    if (!this.value) {
      return
    }

    const [err, r] = await client.describeFile({
      body: { id: this.value }
    })

    if (err) {
      handleErrorH5(err)
      return
    }

    this.file = {
      id: this.value,
      name: r.data.name,
      url: this.imageURL(this.value)
    }
  },
  methods: {
    triggerUploader() {
      if (this.file) {
        return
      }

      this.$refs.realUploader.click()
    },
    handleFileChange(e) {
      const rawFile = e.target.files[0]
      this.uploadFile(rawFile)
    },
    async uploadFile(rawFile) {
      const formData = new FormData()
      formData.append('file', rawFile)

      this.uploading = true
      const [err, r] = await client.uploadFile({
        body: formData
      })
      this.uploading = false

      if (err) {
        handleErrorH5(err)
        return
      }

      this.file = {
        id: r.data.fileId,
        name: rawFile.name,
        url: this.imageURL(r.data.fileId)
      }

      this.$emit('input', r.data.fileId)
    },
    remove() {
      this.file = null
      this.uploading = false
      this.$emit('input', '')
    },
    imageURL(fileId) {
      return `${window.env?.apiPath}/api/public/previewFile/${fileId}`
    }
  }
}
</script>

<style></style>
