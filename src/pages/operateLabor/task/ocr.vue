<template>
  <div class="ocr-page">
    <div class="header">
      <h2>实名认证</h2>
      <p class="subtitle">请上传身份证照片进行实名认证</p>
    </div>
    <div class="upload-section">
      <ImageUploader v-model="frontImage">
        <template #uploadButton>
          <div class="upload-item">
            <div class="upload-area" @click="uploadFront">
              <div class="id-card-placeholder front">
                <div class="card-content">
                  <div class="avatar-placeholder">
                    <i class="icon-user">👤</i>
                  </div>
                  <div class="info-lines">
                    <div class="line"></div>
                    <div class="line"></div>
                    <div class="line"></div>
                  </div>
                </div>
                <div class="upload-icon">
                  <span class="plus-icon">+</span>
                </div>
              </div>
            </div>
            <p class="upload-text">点击上传身份证人像面</p>
          </div>
        </template>
        <template #preview="{ file, remove }">
          <div class="id-card-placeholder front">
            <img
              style="width: 284px; height: 184px"
              :src="file.url"
              class="uploaded-image"
            />
          </div>
          <div class="upload-text"><a @click="remove">删除</a></div>
        </template>
      </ImageUploader>

      <!-- 身份证国徽面 -->
      <ImageUploader v-model="backImage">
        <template #uploadButton>
          <div class="upload-item">
            <div class="upload-area" @click="uploadBack">
              <div class="id-card-placeholder back">
                <div class="card-content">
                  <div class="emblem-placeholder">
                    <i class="icon-emblem">🏛️</i>
                  </div>
                  <div class="info-lines">
                    <div class="line long"></div>
                    <div class="line medium"></div>
                    <div class="line short"></div>
                  </div>
                </div>
                <div class="upload-icon">
                  <span class="plus-icon">+</span>
                </div>
              </div>
            </div>
            <p class="upload-text">点击上传身份证国徽面</p>
          </div>
        </template>
        <template #preview="{ file, remove }">
          <div class="id-card-placeholder front">
            <img
              style="width: 284px; height: 184px"
              :src="file.url"
              class="uploaded-image"
            />
          </div>
          <div class="upload-text"><a @click="remove">删除</a></div>
        </template>
      </ImageUploader>
    </div>

    <!-- 下一步按钮 -->
    <div class="button-section">
      <Button class="next-btn" :disabled="!canProceed" @click="handleNext" :loading="loading" loading-type="spinner">
        下一步
      </Button>
    </div>
  </div>
</template>

<script>
import ImageUploader from './ocr/uploader.vue'
import { Button, Toast } from 'vant'
import handleError from '../../../helpers/handleErrorH5'
import makeClient from '../../../services/operateLabor/makeClient'
const client = makeClient()

export default {
  name: 'TaskOcr',

  components: {
    ImageUploader,
    Button
  },

  data() {
    return {
      frontImage: null,
      backImage: null,
      showUploader: false,
      currentUploadType: null, // 'front' or 'back'
      loading: false
    }
  },

  computed: {
    canProceed() {
      return this.frontImage && this.backImage
    }
  },

  methods: {
    uploadFront() {
      // this.currentUploadType = 'front'
      // this.showUploader = true
    },

    uploadBack() {
      this.currentUploadType = 'back'
      this.showUploader = true
    },

    handleImageUploaded(files) {
      if (files && files.length > 0) {
        const file = files[0]
        if (this.currentUploadType === 'front') {
          this.frontImage = file.url || URL.createObjectURL(file.file)
        } else if (this.currentUploadType === 'back') {
          this.backImage = file.url || URL.createObjectURL(file.file)
        }
      }
      this.showUploader = false
      this.currentUploadType = null
    },

    async handleNext() {
      if (!this.canProceed) return
      this.loading = true

      const [err, r] = await client.ocrIdentify({
        body: {
          personalIdImg: this.frontImage,
          nationalEmblemImg: this.backImage,
        }
      })
      this.loading = false
      if(err) {
        handleError(err)
        return
      }
      if(!r.data.success) {
        Toast(r.data.errorMessage)
        return
      }

      Toast('身份识别成功')
      this.$router.push({
        path: '/ocrIdentify',
        query: {
          frontImage: this.frontImage,
          backImage: this.backImage,
          name: r.data.name,
          idNo: r.data.idNo,
          flowNo: r.data.flowNo
        }
      })
    }
  }
}
</script>

<style scoped>
@import './styles.css';

.ocr-page {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 40px;
  padding-top: 20px;
}

.header h2 {
  font-size: 24px;
  color: #333;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.upload-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 40px;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.upload-item {
  text-align: center;
}

.upload-area {
  cursor: pointer;
  transition: transform 0.2s;
}

.upload-area:active {
  transform: scale(0.98);
}

.id-card-placeholder {
  width: 280px;
  height: 180px;
  border: 2px dashed #ddd;
  border-radius: 12px;
  background: white;
  position: relative;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-content {
  display: flex;
  align-items: center;
  gap: 20px;
  opacity: 0.3;
}

.avatar-placeholder,
.emblem-placeholder {
  width: 60px;
  height: 80px;
  background: #f0f0f0;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.emblem-placeholder {
  height: 60px;
}

.info-lines {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.line {
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
}

.line {
  width: 120px;
}

.line.long {
  width: 140px;
}

.line.medium {
  width: 100px;
}

.line.short {
  width: 80px;
}

.upload-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60px;
  height: 60px;
  background: rgba(66, 133, 244, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plus-icon {
  font-size: 32px;
  color: #4285f4;
  font-weight: 300;
}

.uploaded-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 10px;
}

.upload-text {
  margin-top: 15px;
  font-size: 16px;
  color: #666;
  font-weight: 500;
}

.button-section {
  padding: 20px 0 40px 0;
  max-width: 400px;
  margin: 0 auto;
  width: 100%;
}

.next-btn {
  width: 100%;
  height: 50px;
  background: #4285f4;
  color: white;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
}

.next-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.next-btn:not(:disabled):hover {
  background: #3367d6;
  transform: translateY(-1px);
}
</style>
