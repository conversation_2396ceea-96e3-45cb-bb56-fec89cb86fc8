@font-face {
  font-family: "iconfont"; /* Project id 4003582 */
  src: url('iconfont.woff2?t=1691044827317') format('woff2'),
       url('iconfont.woff?t=1691044827317') format('woff'),
       url('iconfont.ttf?t=1691044827317') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-secure-full:before {
  content: "\e990";
}

.icon-statistics-full:before {
  content: "\e991";
}

.icon-clock-in-full:before {
  content: "\e992";
}

.icon-color-attendance-make-up-card:before {
  content: "\e98b";
}

.icon-color-work-overtime:before {
  content: "\e98c";
}

.icon-color-ask-for-leave:before {
  content: "\e98d";
}

.icon-color-application-for-going-out:before {
  content: "\e98e";
}

.icon-color-be-away-on-official-business:before {
  content: "\e98f";
}

.icon-position-full:before {
  content: "\e98a";
}

.icon-clock-in-details-full:before {
  content: "\e986";
}

.icon-calendar-full:before {
  content: "\e987";
}

.icon-rest-full:before {
  content: "\e988";
}

.icon-photograph-full:before {
  content: "\e989";
}

.icon-function-attendance-statistics:before {
  content: "\e97c";
}

.icon-function-attendance-make-up-card:before {
  content: "\e97d";
}

.icon-function-apply-to-become-a-regular-employee:before {
  content: "\e97e";
}

.icon-function-apply-for-nvoice:before {
  content: "\e97f";
}

.icon-function-approval-management:before {
  content: "\e980";
}

.icon-function-apply-for-the-use-of-a-seal:before {
  content: "\e981";
}

.icon-function-apply-for-imprest:before {
  content: "\e982";
}

.icon-function-application-for-going-out:before {
  content: "\e983";
}

.icon-function-add-mployee:before {
  content: "\e984";
}

.icon-function-add-training-plan:before {
  content: "\e985";
}

.icon-function-assessment-settings:before {
  content: "\e972";
}

.icon-function-cc-to-me:before {
  content: "\e973";
}

.icon-function-batch-payment:before {
  content: "\e974";
}

.icon-function-business-travel-reimbursement:before {
  content: "\e975";
}

.icon-function-background-check:before {
  content: "\e976";
}

.icon-function-assessment-plan:before {
  content: "\e977";
}

.icon-a-function-be-away-on-official-business:before {
  content: "\e978";
}

.icon-function-ask-for-leave:before {
  content: "\e979";
}

.icon-function-attendance-clock-in:before {
  content: "\e97a";
}

.icon-function-assessment-report:before {
  content: "\e97b";
}

.icon-function-data-statistics:before {
  content: "\e969";
}

.icon-function-corporate-group-insurance:before {
  content: "\e96a";
}

.icon-function-default:before {
  content: "\e96b";
}

.icon-function-contract-agreement:before {
  content: "\e96c";
}

.icon-function-employee-certification:before {
  content: "\e96d";
}

.icon-function-become-a-regular-worker:before {
  content: "\e96e";
}

.icon-function-business-card-application:before {
  content: "\e96f";
}

.icon-function-contract-management:before {
  content: "\e970";
}

.icon-function-borrowing-from-the-company:before {
  content: "\e971";
}

.icon-function-expert-consultation:before {
  content: "\e95f";
}

.icon-function-ding-flexible-employment:before {
  content: "\e960";
}

.icon-function-custom-train:before {
  content: "\e961";
}

.icon-function-expense-reimbursement:before {
  content: "\e962";
}

.icon-function-ding-signing:before {
  content: "\e963";
}

.icon-function-corporate-welfare:before {
  content: "\e964";
}

.icon-function-enterprise-settings:before {
  content: "\e965";
}

.icon-function-cost-control:before {
  content: "\e966";
}

.icon-function-daily-report:before {
  content: "\e967";
}

.icon-function-examine-and-approve:before {
  content: "\e968";
}

.icon-function-more:before {
  content: "\e955";
}

.icon-function-financial-special-matters:before {
  content: "\e956";
}

.icon-function-individual-income-tax-declaration:before {
  content: "\e957";
}

.icon-function-initiated-by-me:before {
  content: "\e958";
}

.icon-function-learning-management:before {
  content: "\e959";
}

.icon-function-expense-approval:before {
  content: "\e95a";
}

.icon-function-general-approval:before {
  content: "\e95b";
}

.icon-function-import-roster:before {
  content: "\e95c";
}

.icon-function-employee-health:before {
  content: "\e95d";
}

.icon-function-excess-fees:before {
  content: "\e95e";
}

.icon-function-personal-profile:before {
  content: "\e94b";
}

.icon-function-payroll:before {
  content: "\e94c";
}

.icon-function-depart:before {
  content: "\e94d";
}

.icon-function-contract-pproval:before {
  content: "\e94e";
}

.icon-function-online-payroll:before {
  content: "\e94f";
}

.icon-function-my-processed:before {
  content: "\e950";
}

.icon-function-financial-records:before {
  content: "\e951";
}

.icon-function-office-supplies-application:before {
  content: "\e952";
}

.icon-function-financial-reimbursement:before {
  content: "\e953";
}

.icon-function-management-permission-application:before {
  content: "\e954";
}

.icon-function-pending-my-approval:before {
  content: "\e941";
}

.icon-function-hao-fu-dou:before {
  content: "\e942";
}

.icon-function-performance:before {
  content: "\e943";
}

.icon-function-org-management:before {
  content: "\e944";
}

.icon-function-personal:before {
  content: "\e945";
}

.icon-function-monthly-report:before {
  content: "\e946";
}

.icon-function-personnel-matters:before {
  content: "\e947";
}

.icon-function-payslip:before {
  content: "\e948";
}

.icon-function-payment-application:before {
  content: "\e949";
}

.icon-function-onboarding-approval:before {
  content: "\e94a";
}

.icon-function-selection-services:before {
  content: "\e93f";
}

.icon-function-process-onboarding:before {
  content: "\e940";
}

.icon-function-testing-and-training-center:before {
  content: "\e938";
}

.icon-function-salary-setting:before {
  content: "\e939";
}

.icon-function-report-forms:before {
  content: "\e93a";
}

.icon-function-train:before {
  content: "\e93b";
}

.icon-function-process-management:before {
  content: "\e93c";
}

.icon-function-pre-employment-registration:before {
  content: "\e93d";
}

.icon-function-travel-on-business:before {
  content: "\e93e";
}

.icon-function-social-security-provident-fund:before {
  content: "\e92d";
}

.icon-function-recruit:before {
  content: "\e92e";
}

.icon-function-raise-a-salary:before {
  content: "\e92f";
}

.icon-function-yi-qi-fu:before {
  content: "\e930";
}

.icon-function-salaries-and-taxes:before {
  content: "\e931";
}

.icon-function-rights-management:before {
  content: "\e932";
}

.icon-function-taxpayer-information:before {
  content: "\e933";
}

.icon-function-weekly-report:before {
  content: "\e934";
}

.icon-function-vehicle-application:before {
  content: "\e935";
}

.icon-function-purchasing-requisition:before {
  content: "\e936";
}

.icon-function-resignation-application:before {
  content: "\e937";
}

.icon-function-training-settings:before {
  content: "\e923";
}

.icon-function-work-overtime:before {
  content: "\e924";
}

.icon-function-transfer-position:before {
  content: "\e925";
}

.icon-function-wage-card:before {
  content: "\e926";
}

.icon-function-staff-management:before {
  content: "\e927";
}

.icon-function-staff-management-settings:before {
  content: "\e928";
}

.icon-function-write-off-request:before {
  content: "\e929";
}

.icon-function-resignation-handover:before {
  content: "\e92a";
}

.icon-function-service-sector:before {
  content: "\e92b";
}

.icon-function-Off-duty-shift-change:before {
  content: "\e92c";
}

.icon-function-xiang-yi-tian-xia:before {
  content: "\e91e";
}

.icon-function-special-additional-deduction:before {
  content: "\e91f";
}

.icon-function-personnel-report:before {
  content: "\e920";
}

.icon-function-social-security-adjustment:before {
  content: "\e921";
}

.icon-function-salary-accounting:before {
  content: "\e922";
}

.icon-gongzitiao:before {
  content: "\e91d";
}

.icon-application-phone:before {
  content: "\e91c";
}

.icon-home-full:before {
  content: "\e91b";
}

.icon-to-do-list-full:before {
  content: "\e91a";
}

.icon-user-full:before {
  content: "\e919";
}

.icon-svg-file-docx:before {
  content: "\e8e2";
}

.icon-svg-file-et:before {
  content: "\e8e3";
}

.icon-svg-file-3gp:before {
  content: "\e8e4";
}

.icon-svg-file-div:before {
  content: "\e8e5";
}

.icon-svg-file-excel:before {
  content: "\e8e6";
}

.icon-svg-file-apk:before {
  content: "\e8e7";
}

.icon-svg-file-ai:before {
  content: "\e8e8";
}

.icon-svg-file-gsheet:before {
  content: "\e8e9";
}

.icon-svg-file-folder:before {
  content: "\e8ea";
}

.icon-svg-file-exe:before {
  content: "\e8eb";
}

.icon-svg-file-bmp:before {
  content: "\e8ec";
}

.icon-svg-file-eps:before {
  content: "\e8ed";
}

.icon-svg-file-markdown:before {
  content: "\e8ee";
}

.icon-svg-file-overlay:before {
  content: "\e8ef";
}

.icon-svg-file-jpg:before {
  content: "\e8f0";
}

.icon-svg-file-flash:before {
  content: "\e8f1";
}

.icon-svg-file-csv:before {
  content: "\e8f2";
}

.icon-svg-file-html:before {
  content: "\e8f3";
}

.icon-svg-file-folder_open:before {
  content: "\e8f4";
}

.icon-svg-file-gpres:before {
  content: "\e8f5";
}

.icon-svg-file-pack:before {
  content: "\e8f6";
}

.icon-svg-file-log:before {
  content: "\e8f7";
}

.icon-svg-file-attachment:before {
  content: "\e8f8";
}

.icon-svg-file-link:before {
  content: "\e8f9";
}

.icon-svg-file-gif:before {
  content: "\e8fa";
}

.icon-svg-file-ppt:before {
  content: "\e8fb";
}

.icon-svg-file-box_notes:before {
  content: "\e8fc";
}

.icon-svg-file-gz:before {
  content: "\e8fd";
}

.icon-svg-file-dps:before {
  content: "\e8fe";
}

.icon-svg-file-pdf:before {
  content: "\e8ff";
}

.icon-svg-file-gform:before {
  content: "\e900";
}

.icon-svg-file-txt:before {
  content: "\e901";
}

.icon-svg-file-word:before {
  content: "\e902";
}

.icon-svg-file-quip_sheet:before {
  content: "\e903";
}

.icon-svg-file-rtf:before {
  content: "\e904";
}

.icon-svg-file-quip_doc:before {
  content: "\e905";
}

.icon-svg-file-tar:before {
  content: "\e906";
}

.icon-svg-file-mp4:before {
  content: "\e907";
}

.icon-svg-file-gdocs:before {
  content: "\e908";
}

.icon-svg-file-unknown:before {
  content: "\e909";
}

.icon-svg-file-stypi:before {
  content: "\e90a";
}

.icon-svg-file-ofd:before {
  content: "\e90b";
}

.icon-svg-file-webex:before {
  content: "\e90c";
}

.icon-svg-file-xls:before {
  content: "\e90d";
}

.icon-svg-file-vis:before {
  content: "\e90e";
}

.icon-svg-file-ps:before {
  content: "\e90f";
}

.icon-svg-file-mp3:before {
  content: "\e910";
}

.icon-svg-file-key:before {
  content: "\e911";
}

.icon-svg-file-pgs:before {
  content: "\e912";
}

.icon-svg-file-xml:before {
  content: "\e913";
}

.icon-svg-file-wps:before {
  content: "\e914";
}

.icon-svg-file-zip-1:before {
  content: "\e915";
}

.icon-svg-file-slide:before {
  content: "\e916";
}

.icon-svg-file-xmind:before {
  content: "\e917";
}

.icon-svg-file-zip:before {
  content: "\e918";
}

.icon-base-loading1:before {
  content: "\e8e1";
}

.icon-weather-dust:before {
  content: "\e8d1";
}

.icon-weather-moon:before {
  content: "\e8d2";
}

.icon-weather-mist:before {
  content: "\e8d3";
}

.icon-weather-hurricane:before {
  content: "\e8d4";
}

.icon-weather-snowsquall:before {
  content: "\e8d5";
}

.icon-weather-gale:before {
  content: "\e8d6";
}

.icon-weather-smog:before {
  content: "\e8d7";
}

.icon-weather-wind:before {
  content: "\e8d8";
}

.icon-weather-rain:before {
  content: "\e8d9";
}

.icon-weather-sandstorm:before {
  content: "\e8da";
}

.icon-weather-storm:before {
  content: "\e8db";
}

.icon-weather-sunset:before {
  content: "\e8dc";
}

.icon-weather-sun:before {
  content: "\e8dd";
}

.icon-weather-sunrise:before {
  content: "\e8de";
}

.icon-weather-freezing-rain:before {
  content: "\e8df";
}

.icon-weather-typhoon:before {
  content: "\e8e0";
}

.icon-remind-check-circle:before {
  content: "\e8c3";
}

.icon-remind-close-circle-line-none:before {
  content: "\e8c4";
}

.icon-remind-accident-report:before {
  content: "\e8c5";
}

.icon-remind-equal-circle:before {
  content: "\e8c6";
}

.icon-remind-complete:before {
  content: "\e8c7";
}

.icon-remind-plus-circle:before {
  content: "\e8c8";
}

.icon-remind-close-circle:before {
  content: "\e8c9";
}

.icon-remind-info-circle:before {
  content: "\e8ca";
}

.icon-remind-divide-circle:before {
  content: "\e8cb";
}

.icon-remind-stop:before {
  content: "\e8cc";
}

.icon-remind-minus-circle:before {
  content: "\e8cd";
}

.icon-remind-exclamation-circle:before {
  content: "\e8ce";
}

.icon-remind-close:before {
  content: "\e8cf";
}

.icon-remind-question-circle:before {
  content: "\e8d0";
}

.icon-media-operation-next:before {
  content: "\e89c";
}

.icon-media-operation-fast-forward:before {
  content: "\e89d";
}

.icon-media-operation-play-circle:before {
  content: "\e89e";
}

.icon-media-notification-on:before {
  content: "\e89f";
}

.icon-media-audio-file:before {
  content: "\e8a0";
}

.icon-media-file-music:before {
  content: "\e8a1";
}

.icon-media-operation-last:before {
  content: "\e8a2";
}

.icon-media-musical-notation:before {
  content: "\e8a3";
}

.icon-media-add-video-camera:before {
  content: "\e8a4";
}

.icon-media-change:before {
  content: "\e8a5";
}

.icon-media-notification-off:before {
  content: "\e8a6";
}

.icon-media-file-video:before {
  content: "\e8a7";
}

.icon-media-music-list:before {
  content: "\e8a8";
}

.icon-media-operation-pause-circle:before {
  content: "\e8a9";
}

.icon-media-operation-play:before {
  content: "\e8aa";
}

.icon-media-four-k:before {
  content: "\e8ab";
}

.icon-media-file-online:before {
  content: "\e8ac";
}

.icon-media-record-stop:before {
  content: "\e8ad";
}

.icon-media-Frame:before {
  content: "\e8ae";
}

.icon-media-palylist-shuffle:before {
  content: "\e8af";
}

.icon-media-record:before {
  content: "\e8b0";
}

.icon-media-edit-song:before {
  content: "\e8b1";
}

.icon-media-soundwave:before {
  content: "\e8b2";
}

.icon-media-voice-input:before {
  content: "\e8b3";
}

.icon-media-operation-rewind:before {
  content: "\e8b4";
}

.icon-media-video-camera:before {
  content: "\e8b5";
}

.icon-media-video-chat:before {
  content: "\e8b6";
}

.icon-media-play-later:before {
  content: "\e8b7";
}

.icon-media-playlist:before {
  content: "\e8b8";
}

.icon-media-palylist-repeat-one:before {
  content: "\e8b9";
}

.icon-media-wifi-on:before {
  content: "\e8ba";
}

.icon-media-voice-volume:before {
  content: "\e8bb";
}

.icon-media-palylist-loop:before {
  content: "\e8bc";
}

.icon-media-voice-mute-alt:before {
  content: "\e8bd";
}

.icon-media-wifi-off:before {
  content: "\e8be";
}

.icon-media-voice-call:before {
  content: "\e8bf";
}

.icon-media-operation-sound-balance:before {
  content: "\e8c0";
}

.icon-media-voice-mute:before {
  content: "\e8c1";
}

.icon-media-watch:before {
  content: "\e8c2";
}

.icon-edit-gender-woman:before {
  content: "\e82e";
}

.icon-edit-font-size-h2:before {
  content: "\e82f";
}

.icon-edit-font-underline:before {
  content: "\e830";
}

.icon-edit-font-text-alt:before {
  content: "\e831";
}

.icon-edit-font-size-h5:before {
  content: "\e832";
}

.icon-edit-font-strikethrough:before {
  content: "\e833";
}

.icon-edit-font-line-height:before {
  content: "\e834";
}

.icon-edit-font-size-h8:before {
  content: "\e835";
}

.icon-edit-font-size-h3:before {
  content: "\e836";
}

.icon-edit-font-size-h9:before {
  content: "\e837";
}

.icon-edit-insert-at:before {
  content: "\e838";
}

.icon-edit-grid:before {
  content: "\e839";
}

.icon-edit-font-size-ha:before {
  content: "\e83a";
}

.icon-edit-insert-attachment:before {
  content: "\e83b";
}

.icon-edit-gender-man:before {
  content: "\e83c";
}

.icon-edit-insert-formula:before {
  content: "\e83d";
}

.icon-edit-insert-option:before {
  content: "\e83e";
}

.icon-edit-import-alt:before {
  content: "\e83f";
}

.icon-edit-insert-code-block:before {
  content: "\e840";
}

.icon-edit-insert-command:before {
  content: "\e841";
}

.icon-edit-insert-qrcode:before {
  content: "\e842";
}

.icon-edit-font-word-wrap:before {
  content: "\e843";
}

.icon-edit-interaction-copy:before {
  content: "\e844";
}

.icon-edit-insert-sdk:before {
  content: "\e845";
}

.icon-edit-interaction-crop:before {
  content: "\e846";
}

.icon-edit-insert-code-release-managment:before {
  content: "\e847";
}

.icon-edit-insert-routes:before {
  content: "\e848";
}

.icon-edit-insert-codes:before {
  content: "\e849";
}

.icon-edit-insert-subordinates:before {
  content: "\e84a";
}

.icon-edit-interaction-paste:before {
  content: "\e84b";
}

.icon-edit-insert-comment:before {
  content: "\e84c";
}

.icon-edit-fontcolor:before {
  content: "\e84d";
}

.icon-edit-interaction-batch-upload:before {
  content: "\e84e";
}

.icon-edit-interaction-create-copy:before {
  content: "\e84f";
}

.icon-edit-insert-route:before {
  content: "\e850";
}

.icon-edit-insert-text:before {
  content: "\e851";
}

.icon-edit-interaction-export-alt:before {
  content: "\e852";
}

.icon-edit-interaction-download-desktop:before {
  content: "\e853";
}

.icon-edit-layout-responsive:before {
  content: "\e854";
}

.icon-edit-meeting-room:before {
  content: "\e855";
}

.icon-edit-intersect:before {
  content: "\e856";
}

.icon-edit-mind-mapping:before {
  content: "\e857";
}

.icon-edit-list-unordered:before {
  content: "\e858";
}

.icon-edit-mouse-direction-alt:before {
  content: "\e859";
}

.icon-edit-interaction-translation:before {
  content: "\e85a";
}

.icon-edit-insert-quote:before {
  content: "\e85b";
}

.icon-edit-mouse-drag-arrow:before {
  content: "\e85c";
}

.icon-edit-mirror:before {
  content: "\e85d";
}

.icon-edit-meeting-room-off:before {
  content: "\e85e";
}

.icon-edit-operation-refresh:before {
  content: "\e85f";
}

.icon-edit-mosaic:before {
  content: "\e860";
}

.icon-edit-layout-scroll:before {
  content: "\e861";
}

.icon-edit-new-window:before {
  content: "\e862";
}

.icon-edit-link:before {
  content: "\e863";
}

.icon-edit-operation-print:before {
  content: "\e864";
}

.icon-edit-operation-undo:before {
  content: "\e865";
}

.icon-edit-new-theme:before {
  content: "\e866";
}

.icon-edit-operation-exit-rich-text:before {
  content: "\e867";
}

.icon-edit-mysql:before {
  content: "\e868";
}

.icon-edit-operation-revert:before {
  content: "\e869";
}

.icon-edit-list-ordered:before {
  content: "\e86a";
}

.icon-edit-Outline-inner-borders:before {
  content: "\e86b";
}

.icon-edit-operation-redo:before {
  content: "\e86c";
}

.icon-edit-Outline-clear-borders:before {
  content: "\e86d";
}

.icon-edit-mouse-icon:before {
  content: "\e86e";
}

.icon-edit-operation-rich-text:before {
  content: "\e86f";
}

.icon-edit-select-top-choice:before {
  content: "\e870";
}

.icon-edit-mouse-direction:before {
  content: "\e871";
}

.icon-edit-Outline-outer-borders:before {
  content: "\e872";
}

.icon-edit-Outline-left-border:before {
  content: "\e873";
}

.icon-edit-overflow:before {
  content: "\e874";
}

.icon-edit-shape-rectangle:before {
  content: "\e875";
}

.icon-edit-Outline-top-border:before {
  content: "\e876";
}

.icon-edit-Outline-bottom-border:before {
  content: "\e877";
}

.icon-edit-operation-save-file:before {
  content: "\e878";
}

.icon-edit-shape-polygon:before {
  content: "\e879";
}

.icon-edit-Outline-right-border:before {
  content: "\e87a";
}

.icon-edit-shape-ellipse:before {
  content: "\e87b";
}

.icon-edit-select-all:before {
  content: "\e87c";
}

.icon-edit-sort-ascending-sorting:before {
  content: "\e87d";
}

.icon-edit-shape-rectangle-1:before {
  content: "\e87e";
}

.icon-edit-sort-ascending-alphabet:before {
  content: "\e87f";
}

.icon-edit-select-down-choice:before {
  content: "\e880";
}

.icon-edit-shape-morph:before {
  content: "\e881";
}

.icon-edit-Outline-show-borders:before {
  content: "\e882";
}

.icon-edit-spreadsheet-freeze-first-column:before {
  content: "\e883";
}

.icon-edit-spreadsheet-add-playlist:before {
  content: "\e884";
}

.icon-edit-sort-Descending-sorting:before {
  content: "\e885";
}

.icon-edit-sort-descending-alphabet:before {
  content: "\e886";
}

.icon-edit-spreadsheet-freeze-columns:before {
  content: "\e887";
}

.icon-edit-shape-ellipse-1:before {
  content: "\e888";
}

.icon-edit-spreadsheet-add:before {
  content: "\e889";
}

.icon-edit-spreadsheet-selected:before {
  content: "\e88a";
}

.icon-edit-text-align-center:before {
  content: "\e88b";
}

.icon-edit-spreadsheet-freeze-top-row:before {
  content: "\e88c";
}

.icon-edit-spreadsheet-file:before {
  content: "\e88d";
}

.icon-edit-text-ailgn-right:before {
  content: "\e88e";
}

.icon-edit-text-ailgn-justified:before {
  content: "\e88f";
}

.icon-edit-text-align-left:before {
  content: "\e890";
}

.icon-edit-text-align-details:before {
  content: "\e891";
}

.icon-edit-shape-rectangle-2:before {
  content: "\e892";
}

.icon-edit-spreadsheet-insert-row:before {
  content: "\e893";
}

.icon-edit-spreadsheet-separate-window:before {
  content: "\e894";
}

.icon-edit-union:before {
  content: "\e895";
}

.icon-edit-Sync:before {
  content: "\e896";
}

.icon-edit-spreadsheet-merge-cells:before {
  content: "\e897";
}

.icon-edit-tool-knife:before {
  content: "\e898";
}

.icon-edit-truncation:before {
  content: "\e899";
}

.icon-edit-tool-scissor:before {
  content: "\e89a";
}

.icon-edit-tool-pen:before {
  content: "\e89b";
}

.icon-edit-align-left:before {
  content: "\e808";
}

.icon-edit-align-horizontal-centers:before {
  content: "\e809";
}

.icon-edit-align-right:before {
  content: "\e80a";
}

.icon-edit-align-bottom:before {
  content: "\e80b";
}

.icon-edit-add-subscription:before {
  content: "\e80c";
}

.icon-edit-bring-backward:before {
  content: "\e80d";
}

.icon-edit-bg-colors:before {
  content: "\e80e";
}

.icon-edit-chart-block:before {
  content: "\e80f";
}

.icon-edit-bring-forward:before {
  content: "\e810";
}

.icon-edit-blockchain:before {
  content: "\e811";
}

.icon-edit-chart:before {
  content: "\e812";
}

.icon-edit-band-width:before {
  content: "\e813";
}

.icon-edit-demonstration:before {
  content: "\e814";
}

.icon-edit-align-vertical-centers:before {
  content: "\e815";
}

.icon-edit-dev-process:before {
  content: "\e816";
}

.icon-edit-command-line:before {
  content: "\e817";
}

.icon-edit-code-square:before {
  content: "\e818";
}

.icon-edit-align-top:before {
  content: "\e819";
}

.icon-edit-face:before {
  content: "\e81a";
}

.icon-edit-connection:before {
  content: "\e81b";
}

.icon-edit-flip-horizontal:before {
  content: "\e81c";
}

.icon-edit-data-analysis:before {
  content: "\e81d";
}

.icon-edit-font-bold:before {
  content: "\e81e";
}

.icon-edit-font-brush:before {
  content: "\e81f";
}

.icon-edit-filter:before {
  content: "\e820";
}

.icon-edit-duplicate:before {
  content: "\e821";
}

.icon-edit-eraser:before {
  content: "\e822";
}

.icon-edit-font-font-colors:before {
  content: "\e823";
}

.icon-edit-flip-vertical:before {
  content: "\e824";
}

.icon-edit-font-bg:before {
  content: "\e825";
}

.icon-edit-font-line:before {
  content: "\e826";
}

.icon-edit-font-font:before {
  content: "\e827";
}

.icon-edit-font-italic:before {
  content: "\e828";
}

.icon-edit-font-size-h1:before {
  content: "\e829";
}

.icon-edit-font-size-h4:before {
  content: "\e82a";
}

.icon-edit-font-font-select:before {
  content: "\e82b";
}

.icon-edit-font-size-h6:before {
  content: "\e82c";
}

.icon-edit-font-size-h7:before {
  content: "\e82d";
}

.icon-direction-arrow-border-double-left:before {
  content: "\e7de";
}

.icon-direction-arrow-border-double-down:before {
  content: "\e7df";
}

.icon-direction-arrow-border-double-right:before {
  content: "\e7e0";
}

.icon-direction-arrow-border-double-up:before {
  content: "\e7e1";
}

.icon-direction-arrow-border-up:before {
  content: "\e7e2";
}

.icon-direction-arrow-border-left:before {
  content: "\e7e3";
}

.icon-direction-arrow-caret-left:before {
  content: "\e7e4";
}

.icon-direction-arrow-caret-up:before {
  content: "\e7e5";
}

.icon-direction-arrow-border-right:before {
  content: "\e7e6";
}

.icon-direction-arrow-caret-down:before {
  content: "\e7e7";
}

.icon-direction-arrow-caret-right:before {
  content: "\e7e8";
}

.icon-direction-arrow-down-circle:before {
  content: "\e7e9";
}

.icon-direction-interaction-download:before {
  content: "\e7ea";
}

.icon-direction-arrow-left-circle:before {
  content: "\e7eb";
}

.icon-direction-interaction-download-circle:before {
  content: "\e7ec";
}

.icon-direction-arrow-insert-down:before {
  content: "\e7ed";
}

.icon-direction-arrow-up-circle:before {
  content: "\e7ee";
}

.icon-direction-interaction-swap:before {
  content: "\e7ef";
}

.icon-direction-interaction-upload:before {
  content: "\e7f0";
}

.icon-direction-keyboard-on:before {
  content: "\e7f1";
}

.icon-direction-interaction-update-circle:before {
  content: "\e7f2";
}

.icon-direction-interaction-sort:before {
  content: "\e7f3";
}

.icon-direction-arrow-insert-right:before {
  content: "\e7f4";
}

.icon-direction-arrow-right-circle:before {
  content: "\e7f5";
}

.icon-direction-sidebar-unfold:before {
  content: "\e7f6";
}

.icon-direction-keyboard-shortcuts:before {
  content: "\e7f7";
}

.icon-direction-arrow-insert-up:before {
  content: "\e7f8";
}

.icon-direction-to-bottom:before {
  content: "\e7f9";
}

.icon-direction-menu-fold:before {
  content: "\e7fa";
}

.icon-direction-to-top:before {
  content: "\e7fb";
}

.icon-direction-to-right:before {
  content: "\e7fc";
}

.icon-direction-scale:before {
  content: "\e7fd";
}

.icon-direction-sidebar-fold:before {
  content: "\e7fe";
}

.icon-direction-to-vertical-centers:before {
  content: "\e7ff";
}

.icon-direction-to-h-centers:before {
  content: "\e800";
}

.icon-direction-rotate-left:before {
  content: "\e801";
}

.icon-direction-rotate-right:before {
  content: "\e802";
}

.icon-direction-arrow-insert-left:before {
  content: "\e803";
}

.icon-direction-arrow-border-down:before {
  content: "\e804";
}

.icon-direction-menu-unfold:before {
  content: "\e805";
}

.icon-direction-keyboard-off:before {
  content: "\e806";
}

.icon-direction-to-left:before {
  content: "\e807";
}

.icon-base-reply:before {
  content: "\e7b2";
}

.icon-base-safe:before {
  content: "\e7b3";
}

.icon-base-time:before {
  content: "\e7b4";
}

.icon-base-pin:before {
  content: "\e7b5";
}

.icon-base-star:before {
  content: "\e7b6";
}

.icon-base-thunder-bolt:before {
  content: "\e7b7";
}

.icon-base-shutdown:before {
  content: "\e7b8";
}

.icon-base-technology:before {
  content: "\e7b9";
}

.icon-base-user-account:before {
  content: "\e7ba";
}

.icon-base-trophy:before {
  content: "\e7bb";
}

.icon-base-switch:before {
  content: "\e7bc";
}

.icon-base-usb:before {
  content: "\e7bd";
}

.icon-base-tree-unfold:before {
  content: "\e7be";
}

.icon-base-project:before {
  content: "\e7bf";
}

.icon-base-user-contact:before {
  content: "\e7c0";
}

.icon-base-user-add:before {
  content: "\e7c1";
}

.icon-base-to-do:before {
  content: "\e7c2";
}

.icon-base-tuee-unfold:before {
  content: "\e7c3";
}

.icon-base-user-icon:before {
  content: "\e7c4";
}

.icon-base-position-warning:before {
  content: "\e7c5";
}

.icon-base-key:before {
  content: "\e7c6";
}

.icon-base-set:before {
  content: "\e7c7";
}

.icon-base-user-delete:before {
  content: "\e7c8";
}

.icon-base-user-identity:before {
  content: "\e7c9";
}

.icon-base-user-management:before {
  content: "\e7ca";
}

.icon-base-user-group:before {
  content: "\e7cb";
}

.icon-base-user-manager:before {
  content: "\e7cc";
}

.icon-base-user-onbording:before {
  content: "\e7cd";
}

.icon-base-user-roi-analysis:before {
  content: "\e7ce";
}

.icon-base-user-personal-information:before {
  content: "\e7cf";
}

.icon-base-user-batch-activation:before {
  content: "\e7d0";
}

.icon-base-view-card:before {
  content: "\e7d1";
}

.icon-base-user-group-chat:before {
  content: "\e7d2";
}

.icon-base-parking:before {
  content: "\e7d3";
}

.icon-base-view:before {
  content: "\e7d4";
}

.icon-base-wifi-security:before {
  content: "\e7d5";
}

.icon-base-user-reference:before {
  content: "\e7d6";
}

.icon-base-wait:before {
  content: "\e7d7";
}

.icon-base-zoom-out:before {
  content: "\e7d8";
}

.icon-base-unfreeze:before {
  content: "\e7d9";
}

.icon-base-zoom-in:before {
  content: "\e7da";
}

.icon-base-voice:before {
  content: "\e7db";
}

.icon-base-user-developer:before {
  content: "\e7dc";
}

.icon-base-safari:before {
  content: "\e7dd";
}

.icon-base-analysis:before {
  content: "\e722";
}

.icon-base-alarm-on:before {
  content: "\e723";
}

.icon-base-announcement:before {
  content: "\e724";
}

.icon-base-award:before {
  content: "\e725";
}

.icon-base-add:before {
  content: "\e726";
}

.icon-base-application:before {
  content: "\e727";
}

.icon-base-auto-circle-brightness:before {
  content: "\e728";
}

.icon-base-block:before {
  content: "\e729";
}

.icon-base-approval:before {
  content: "\e72a";
}

.icon-base-book:before {
  content: "\e72b";
}

.icon-base-book-alt:before {
  content: "\e72c";
}

.icon-base-business-center:before {
  content: "\e72d";
}

.icon-base-alarm-off:before {
  content: "\e72e";
}

.icon-base-bulb:before {
  content: "\e72f";
}

.icon-base-calendar-time:before {
  content: "\e730";
}

.icon-base-calendar:before {
  content: "\e731";
}

.icon-base-calendar-query:before {
  content: "\e732";
}

.icon-base-card-view:before {
  content: "\e733";
}

.icon-base-calendar-founder:before {
  content: "\e734";
}

.icon-base-change-color:before {
  content: "\e735";
}

.icon-base-camera:before {
  content: "\e736";
}

.icon-base-calendar-to-do:before {
  content: "\e737";
}

.icon-base-calendar-toggle-view:before {
  content: "\e738";
}

.icon-base-clean:before {
  content: "\e739";
}

.icon-base-box-plot:before {
  content: "\e73a";
}

.icon-base-clipboard-error:before {
  content: "\e73b";
}

.icon-base-clipboard-add:before {
  content: "\e73c";
}

.icon-base-calculator:before {
  content: "\e73d";
}

.icon-base-clipboard-delete:before {
  content: "\e73e";
}

.icon-base-clipboard-success:before {
  content: "\e73f";
}

.icon-base-clipboard:before {
  content: "\e740";
}

.icon-base-collection-full:before {
  content: "\e741";
}

.icon-base-calendar-alt:before {
  content: "\e742";
}

.icon-base-contrast-alt:before {
  content: "\e743";
}

.icon-base-cloud-download-alt:before {
  content: "\e744";
}

.icon-base-course-statistics:before {
  content: "\e745";
}

.icon-base-column-height-outlined:before {
  content: "\e746";
}

.icon-base-compass:before {
  content: "\e747";
}

.icon-base-cloud-download:before {
  content: "\e748";
}

.icon-base-currency-credits:before {
  content: "\e749";
}

.icon-base-clipboard-text:before {
  content: "\e74a";
}

.icon-base-currency-cash:before {
  content: "\e74b";
}

.icon-base-comprehensive-evaluation:before {
  content: "\e74c";
}

.icon-base-collection:before {
  content: "\e74d";
}

.icon-base-currency-fund:before {
  content: "\e74e";
}

.icon-base-currency-currency:before {
  content: "\e74f";
}

.icon-base-currency-payment-management:before {
  content: "\e750";
}

.icon-base-currency-housing-subsidy:before {
  content: "\e751";
}

.icon-base-currency-red-packet:before {
  content: "\e752";
}

.icon-base-currency-ready-to-pay:before {
  content: "\e753";
}

.icon-base-currency-wallet:before {
  content: "\e754";
}

.icon-base-currency-payment-report:before {
  content: "\e755";
}

.icon-base-customer-service:before {
  content: "\e756";
}

.icon-base-data-data-visualization:before {
  content: "\e757";
}

.icon-base-data-curve:before {
  content: "\e758";
}

.icon-base-data-percentage:before {
  content: "\e759";
}

.icon-base-currency-profit-model:before {
  content: "\e75a";
}

.icon-base-data-behavior-analysis:before {
  content: "\e75b";
}

.icon-base-currency-social-insurance-management:before {
  content: "\e75c";
}

.icon-base-data-area-chart:before {
  content: "\e75d";
}

.icon-base-data-pie-chart:before {
  content: "\e75e";
}

.icon-base-data-data-traffic:before {
  content: "\e75f";
}

.icon-base-data-market-level:before {
  content: "\e760";
}

.icon-base-data-line-chart:before {
  content: "\e761";
}

.icon-base-data-exhibition:before {
  content: "\e762";
}

.icon-base-deal-circle:before {
  content: "\e763";
}

.icon-base-draft:before {
  content: "\e764";
}

.icon-base-data-table:before {
  content: "\e765";
}

.icon-base-delete:before {
  content: "\e766";
}

.icon-base-currency-payment-check:before {
  content: "\e767";
}

.icon-base-drag-dot-vertical:before {
  content: "\e768";
}

.icon-base-data-profit-model-alt:before {
  content: "\e769";
}

.icon-base-del:before {
  content: "\e76a";
}

.icon-base-email:before {
  content: "\e76b";
}

.icon-base-earth:before {
  content: "\e76c";
}

.icon-base-enter-theme:before {
  content: "\e76d";
}

.icon-base-edit:before {
  content: "\e76e";
}

.icon-base-export:before {
  content: "\e76f";
}

.icon-base-done:before {
  content: "\e770";
}

.icon-base-find-replace:before {
  content: "\e771";
}

.icon-base-enter-theme-1:before {
  content: "\e772";
}

.icon-base-done-alt:before {
  content: "\e773";
}

.icon-base-eye-open:before {
  content: "\e774";
}

.icon-base-find-search:before {
  content: "\e775";
}

.icon-base-enter-theme-2:before {
  content: "\e776";
}

.icon-base-eye-invisible:before {
  content: "\e777";
}

.icon-base-flag-alt:before {
  content: "\e778";
}

.icon-base-english:before {
  content: "\e779";
}

.icon-base-forward:before {
  content: "\e77a";
}

.icon-base-fullscreen-exit:before {
  content: "\e77b";
}

.icon-base-eye-close:before {
  content: "\e77c";
}

.icon-base-fullscreen-in:before {
  content: "\e77d";
}

.icon-base-Frame:before {
  content: "\e77e";
}

.icon-base-funnel-plot:before {
  content: "\e77f";
}

.icon-base-forward-all:before {
  content: "\e780";
}

.icon-base-fire:before {
  content: "\e781";
}

.icon-base-hide-separate-window:before {
  content: "\e782";
}

.icon-base-laptop:before {
  content: "\e783";
}

.icon-base-gold:before {
  content: "\e784";
}

.icon-base-jump-to:before {
  content: "\e785";
}

.icon-base-import:before {
  content: "\e786";
}

.icon-base-kanban:before {
  content: "\e787";
}

.icon-base-history:before {
  content: "\e788";
}

.icon-base-like:before {
  content: "\e789";
}

.icon-base-insert-chart:before {
  content: "\e78a";
}

.icon-base-flag:before {
  content: "\e78b";
}

.icon-base-more-vertical:before {
  content: "\e78c";
}

.icon-base-lock-close-1:before {
  content: "\e78d";
}

.icon-base-more-h:before {
  content: "\e78e";
}

.icon-base-market-quotations:before {
  content: "\e78f";
}

.icon-base-lock-close:before {
  content: "\e790";
}

.icon-base-img:before {
  content: "\e791";
}

.icon-base-lock-open:before {
  content: "\e792";
}

.icon-base-new-message-box:before {
  content: "\e793";
}

.icon-base-knowledge:before {
  content: "\e794";
}

.icon-base-location:before {
  content: "\e795";
}

.icon-base-loading:before {
  content: "\e796";
}

.icon-base-menu:before {
  content: "\e797";
}

.icon-base-new-note-1:before {
  content: "\e798";
}

.icon-base-law:before {
  content: "\e799";
}

.icon-base-memo:before {
  content: "\e79a";
}

.icon-base-information-card:before {
  content: "\e79b";
}

.icon-base-navigation:before {
  content: "\e79c";
}

.icon-base-new-message:before {
  content: "\e79d";
}

.icon-base-preview:before {
  content: "\e79e";
}

.icon-base-plan:before {
  content: "\e79f";
}

.icon-base-outbox:before {
  content: "\e7a0";
}

.icon-base-original-size:before {
  content: "\e7a1";
}

.icon-base-new-banned:before {
  content: "\e7a2";
}

.icon-base-ready-to-deliver:before {
  content: "\e7a3";
}

.icon-base-position:before {
  content: "\e7a4";
}

.icon-base-new-note:before {
  content: "\e7a5";
}

.icon-base-position-correct:before {
  content: "\e7a6";
}

.icon-base-reminders:before {
  content: "\e7a7";
}

.icon-base-rotate-device:before {
  content: "\e7a8";
}

.icon-base-list:before {
  content: "\e7a9";
}

.icon-base-receipt:before {
  content: "\e7aa";
}

.icon-base-position-error:before {
  content: "\e7ab";
}

.icon-base-send:before {
  content: "\e7ac";
}

.icon-base-privacy:before {
  content: "\e7ad";
}

.icon-base-ruler:before {
  content: "\e7ae";
}

.icon-base-share-external:before {
  content: "\e7af";
}

.icon-base-set-tool:before {
  content: "\e7b0";
}

.icon-base-scan:before {
  content: "\e7b1";
}

.icon-application-private-equity:before {
  content: "\e700";
}

.icon-application-pdf-file:before {
  content: "\e701";
}

.icon-application-pre-study:before {
  content: "\e702";
}

.icon-application-ranking:before {
  content: "\e703";
}

.icon-application-push-service:before {
  content: "\e704";
}

.icon-application-reading-list:before {
  content: "\e705";
}

.icon-application-relational-database:before {
  content: "\e706";
}

.icon-application-record-information:before {
  content: "\e707";
}

.icon-application-product:before {
  content: "\e708";
}

.icon-application-report:before {
  content: "\e709";
}

.icon-application-questionnaire:before {
  content: "\e70a";
}

.icon-application-replacement-warranty:before {
  content: "\e70b";
}

.icon-application-rss-subscription:before {
  content: "\e70c";
}

.icon-application-robot-add:before {
  content: "\e70d";
}

.icon-application-robot:before {
  content: "\e70e";
}

.icon-application-repository:before {
  content: "\e70f";
}

.icon-application-risk-warning:before {
  content: "\e710";
}

.icon-application-search-file:before {
  content: "\e711";
}

.icon-application-search-log:before {
  content: "\e712";
}

.icon-application-rss:before {
  content: "\e713";
}

.icon-application-service-analysis:before {
  content: "\e714";
}

.icon-application-shared-folder:before {
  content: "\e715";
}

.icon-application-nuclear:before {
  content: "\e716";
}

.icon-application-shake-device:before {
  content: "\e717";
}

.icon-application-slides:before {
  content: "\e718";
}

.icon-application-security-authorization:before {
  content: "\e719";
}

.icon-application-starred-folder:before {
  content: "\e71a";
}

.icon-application-toolbox:before {
  content: "\e71b";
}

.icon-application-short-task:before {
  content: "\e71c";
}

.icon-application-summary:before {
  content: "\e71d";
}

.icon-application-temperature:before {
  content: "\e71e";
}

.icon-application-upload-log:before {
  content: "\e71f";
}

.icon-application-related-lists:before {
  content: "\e720";
}

.icon-application-terminal:before {
  content: "\e721";
}

.icon-application-add-bookmark:before {
  content: "\e6ab";
}

.icon-application-access-control:before {
  content: "\e6ac";
}

.icon-application-add-to-job:before {
  content: "\e6ad";
}

.icon-application-agenda:before {
  content: "\e6ae";
}

.icon-application-add-folder:before {
  content: "\e6af";
}

.icon-application-add-com-mark:before {
  content: "\e6b0";
}

.icon-application-blacklist:before {
  content: "\e6b1";
}

.icon-application-body-text:before {
  content: "\e6b2";
}

.icon-application-add-to-library:before {
  content: "\e6b3";
}

.icon-application-ai:before {
  content: "\e6b4";
}

.icon-application-book:before {
  content: "\e6b5";
}

.icon-application-add-bookshelf:before {
  content: "\e6b6";
}

.icon-application-cloud-block-storage:before {
  content: "\e6b7";
}

.icon-application-cloud-computing:before {
  content: "\e6b8";
}

.icon-application-cloud-engine:before {
  content: "\e6b9";
}

.icon-application-cloud-parse-success:before {
  content: "\e6ba";
}

.icon-application-api-management:before {
  content: "\e6bb";
}

.icon-application-api-gateway:before {
  content: "\e6bc";
}

.icon-application-console:before {
  content: "\e6bd";
}

.icon-application-coordinate:before {
  content: "\e6be";
}

.icon-application-authorization:before {
  content: "\e6bf";
}

.icon-application-cloud-phone:before {
  content: "\e6c0";
}

.icon-application-cloud-parse:before {
  content: "\e6c1";
}

.icon-application-cloud-editing:before {
  content: "\e6c2";
}

.icon-application-course:before {
  content: "\e6c3";
}

.icon-application-complete-info:before {
  content: "\e6c4";
}

.icon-application-circle-ci:before {
  content: "\e6c5";
}

.icon-application-controller:before {
  content: "\e6c6";
}

.icon-application-create-file:before {
  content: "\e6c7";
}

.icon-application-cloud-update:before {
  content: "\e6c8";
}

.icon-application-case:before {
  content: "\e6c9";
}

.icon-application-cloud-space:before {
  content: "\e6ca";
}

.icon-application-create-record:before {
  content: "\e6cb";
}

.icon-application-cloud-gateway:before {
  content: "\e6cc";
}

.icon-application-data-mining:before {
  content: "\e6cd";
}

.icon-application-code-pen:before {
  content: "\e6ce";
}

.icon-application-credit-card:before {
  content: "\e6cf";
}

.icon-application-description:before {
  content: "\e6d0";
}

.icon-application-delete-document:before {
  content: "\e6d1";
}

.icon-application-delete-folder:before {
  content: "\e6d2";
}

.icon-application-course-information:before {
  content: "\e6d3";
}

.icon-application-data-check:before {
  content: "\e6d4";
}

.icon-application-custom-image:before {
  content: "\e6d5";
}

.icon-application-document:before {
  content: "\e6d6";
}

.icon-application-dimension:before {
  content: "\e6d7";
}

.icon-application-create-service:before {
  content: "\e6d8";
}

.icon-application-drawer:before {
  content: "\e6d9";
}

.icon-application-deploy-associations:before {
  content: "\e6da";
}

.icon-application-file:before {
  content: "\e6db";
}

.icon-application-dynamic-config-center:before {
  content: "\e6dc";
}

.icon-application-firewall:before {
  content: "\e6dd";
}

.icon-application-file-group:before {
  content: "\e6de";
}

.icon-application-first-aid:before {
  content: "\e6df";
}

.icon-application-encrypted-document:before {
  content: "\e6e0";
}

.icon-application-file-upload:before {
  content: "\e6e1";
}

.icon-application-folder-download:before {
  content: "\e6e2";
}

.icon-application-folder:before {
  content: "\e6e3";
}

.icon-application-folder-success:before {
  content: "\e6e4";
}

.icon-application-folder-upload-1:before {
  content: "\e6e5";
}

.icon-application-dynamic-decision-making:before {
  content: "\e6e6";
}

.icon-application-folder-error:before {
  content: "\e6e7";
}

.icon-application-form:before {
  content: "\e6e8";
}

.icon-application-folder-upload:before {
  content: "\e6e9";
}

.icon-application-image-file:before {
  content: "\e6ea";
}

.icon-application-grade:before {
  content: "\e6eb";
}

.icon-application-havip:before {
  content: "\e6ec";
}

.icon-application-hierarchy:before {
  content: "\e6ed";
}

.icon-application-ip:before {
  content: "\e6ee";
}

.icon-application-image:before {
  content: "\e6ef";
}

.icon-application-job:before {
  content: "\e6f0";
}

.icon-application-hardware-server:before {
  content: "\e6f1";
}

.icon-application-import-record:before {
  content: "\e6f2";
}

.icon-application-mark:before {
  content: "\e6f3";
}

.icon-application-mirror-off:before {
  content: "\e6f4";
}

.icon-application-load-balancing:before {
  content: "\e6f5";
}

.icon-application-log-search:before {
  content: "\e6f6";
}

.icon-application-nat-gateway:before {
  content: "\e6f7";
}

.icon-application-office-building:before {
  content: "\e6f8";
}

.icon-application-ops-management:before {
  content: "\e6f9";
}

.icon-application-mirror-on:before {
  content: "\e6fa";
}

.icon-application-plan-analysis:before {
  content: "\e6fb";
}

.icon-application-private-ip:before {
  content: "\e6fc";
}

.icon-application-mindmap:before {
  content: "\e6fd";
}

.icon-application-office-building-off:before {
  content: "\e6fe";
}

.icon-application-question-pool:before {
  content: "\e6ff";
}

